# Finanze.Pro Backend API

Backend service for the Finanze.Pro application, providing financial management APIs and background processing.

## Tech Stack

- **Runtime**: [Bun](https://bun.sh/)
- **Web Framework**: [Hono](https://hono.dev/)
- **Database**: PostgreSQL with [Drizzle ORM](https://orm.drizzle.team/)
- **Background Processing**: [BullMQ](https://docs.bullmq.io/)
- **Validation**: [Zod](https://zod.dev/)
- **API Documentation**: OpenAPI with [@hono/zod-openapi](https://github.com/honojs/middleware/tree/main/packages/zod-openapi)
- **Testing**: [Vitest](https://vitest.dev/)
- **Caching**: Redis

## Prerequisites

- [Bun](https://bun.sh/) runtime (latest version recommended)
- PostgreSQL database
- Redis server

## Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/finanze-pro-backend.git
   cd finanze-pro-backend
   ```

2. Copy the environment file and configure the variables:
   ```bash
   cp .env.example .env
   ```

   Key environment variables:
   - `PORT`: The port on which the server runs (default: 5000)
   - `NODE_ENV`: The environment (development, production, etc.)
   - `APP_ENV`: The application environment (local, staging, production, etc.)
   - `DATABASE_URL`: The PostgreSQL database connection string
   - `JWT_SECRET`: The secret key for JWT authentication
   - `CURRENCYAPICOM_KEY`: API key for currency rate updates
   - `LOG_LEVEL`: The logging level (debug, info, warn, error)

3. Install dependencies:
   ```bash
   bun install
   ```

4. Run database migrations:
   ```bash
   bun run drizzle-kit migrate
   ```

## Running the Application

### Development Mode

1. Start the API server with hot reload:
   ```bash
   bun run dev
   ```

2. Start the worker process (in a separate terminal):
   ```bash
   bun run dev:worker
   ```

### Production Mode

1. Start the API server:
   ```bash
   bun run start
   ```

2. Start the worker process:
   ```bash
   bun run start:worker
   ```

## Testing

Run tests:
```bash
bun run test
```

Run tests with UI:
```bash
bun run test:ui
```

## Utilities

Update currency rates:
```bash
bun run update-rates
```

## Code Quality

- Format code:
  ```bash
  bun run format
  ```

- Lint code:
  ```bash
  bun run lint
  ```

- Fix linting issues:
  ```bash
  bun run lint:fix
  ```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
