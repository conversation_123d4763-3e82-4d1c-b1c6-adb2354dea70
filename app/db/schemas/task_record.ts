import { date, decimal, index, pgEnum, pgTable, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { taskStatuses } from "~/common/schemas";

import { id, timestamps } from "./common";
import { tasks } from "./task";

export const taskStatus = pgEnum("task_status", taskStatuses);

export const taskRecords = pgTable(
  "task_records",
  {
    id: id(),
    taskId: uuid()
      .notNull()
      .references(() => tasks.id, { onDelete: "cascade" }),
    startDate: date().notNull(),
    endDate: date().notNull(),
    status: taskStatus().notNull().default("active"),
    amount: decimal({ precision: 15, scale: 4 }),
    ...timestamps,
  },
  (table) => [
    index("idx_task_records_task_id").on(table.taskId),
    index("idx_task_records_period").on(table.startDate, table.endDate),
    index("idx_task_records_status").on(table.status),
  ]
);

export const taskRecordSelectSchema = createSelectSchema(taskRecords);
