import { date, decimal, index, pgTable, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { budgets } from "./budget";
import { id, timestamps } from "./common";

export const budgetRecords = pgTable(
  "budget_records",
  {
    id: id(),
    budgetId: uuid()
      .notNull()
      .references(() => budgets.id, { onDelete: "cascade" }),
    startDate: date().notNull(),
    endDate: date().notNull(),
    plannedAmount: decimal({ precision: 15, scale: 4 }).notNull(),
    usedAmount: decimal({ precision: 15, scale: 4 }).notNull().default("0.0000"),
    ...timestamps,
  },
  (table) => [
    index("idx_budget_records_for_budget_on_period").on(table.budgetId, table.startDate, table.endDate),
    index("idx_budget_records_period").on(table.startDate, table.endDate),
  ]
);

export const budgetRecordSelectSchema = createSelectSchema(budgetRecords);
