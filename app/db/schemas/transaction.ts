import { date, decimal, index, pgEnum, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { transactionTypes } from "~/common/schemas";

import { accounts } from "./account";
import { categories } from "./category";
import { id, timestamps } from "./common";
import { taskRecords } from "./task_record";
import { users } from "./user";

export const transactionType = pgEnum("transaction_type", transactionTypes);

export const transactions = pgTable(
  "transactions",
  {
    id: id(),
    userId: uuid()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    transactionDate: date().notNull(),
    categoryId: uuid().references(() => categories.id, { onDelete: "set null" }),
    type: transactionType().notNull(),
    description: text(),
    accountId: uuid()
      .notNull()
      .references(() => accounts.id, { onDelete: "cascade" }),
    amount: decimal({ precision: 15, scale: 4 }).notNull(),
    accountToId: uuid().references(() => accounts.id, { onDelete: "cascade" }),
    amountTo: decimal({ precision: 15, scale: 4 }),
    baseAmount: decimal({ precision: 15, scale: 4 }).notNull(),
    baseAmountTo: decimal({ precision: 15, scale: 4 }),
    taskRecordId: uuid().references(() => taskRecords.id, { onDelete: "set null" }),
    ...timestamps,
  },
  (table) => [
    index("idx_transactions_user_id").on(table.userId),
    index("idx_transactions_account_id").on(table.accountId),
    index("idx_transactions_account_to_id").on(table.accountToId),
    index("idx_transactions_category_id").on(table.categoryId),
    index("idx_transactions_date").on(table.transactionDate),
    index("idx_transactions_type").on(table.type),
    index("idx_transactions_task_record_id").on(table.taskRecordId),
  ]
);

export const transactionSelectSchema = createSelectSchema(transactions);
