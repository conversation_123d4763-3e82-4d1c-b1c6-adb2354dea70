import { date, decimal, index, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema, createUpdateSchema } from "drizzle-zod";

import { id, timestamps } from "./common";
import { currency } from "./currency";
import { users } from "./user";

export const goals = pgTable(
  "goals",
  {
    id: id(),
    userId: uuid()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: text().notNull(),
    accounts: uuid().array().notNull(),
    targetAmount: decimal({ precision: 15, scale: 4 }),
    currentAmount: decimal({ precision: 15, scale: 4 }).notNull().default("0.0000"),
    description: text(),
    color: text(),
    iconUrl: text(),
    targetDate: date(),
    currency: currency(),
    ...timestamps,
  },
  (table) => [index("idx_goals_user_id").on(table.userId)]
);

export const goalSelectSchema = createSelectSchema(goals);
export const goalUpdateSchema = createUpdateSchema(goals);
