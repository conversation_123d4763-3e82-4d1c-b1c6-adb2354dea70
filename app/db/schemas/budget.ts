import { boolean, decimal, index, pgEnum, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { budgetPeriods, budgetTypes } from "~/common/schemas";

import { id, timestamps } from "./common";
import { users } from "./user";

export const budgetPeriod = pgEnum("budget_period", budgetPeriods);
export const budgetType = pgEnum("budget_type", budgetTypes);

export const budgets = pgTable(
  "budgets",
  {
    id: id(),
    userId: uuid()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: text().notNull(),
    description: text(),
    period: budgetPeriod().notNull(),
    type: budgetType().notNull(),
    value: decimal({ precision: 15, scale: 4 }).notNull(),
    accounts: uuid().array(),
    isArchived: boolean().notNull().default(false),
    ...timestamps,
  },
  (table) => [index("idx_budgets_user_id").on(table.userId)]
);

export const budgetSelectSchema = createSelectSchema(budgets);
