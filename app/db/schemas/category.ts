import { boolean, index, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { id, timestamps } from "./common";
import { users } from "./user";

export const categories = pgTable(
  "categories",
  {
    id: id(),
    userId: uuid()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    name: text().notNull(),
    color: text(),
    icon: text(),
    isExpense: boolean().notNull().default(true),
    ...timestamps,
  },
  (table) => [index("idx_categories_user_id").on(table.userId)]
);

export const categorySelectSchema = createSelectSchema(categories);
