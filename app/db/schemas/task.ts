import { boolean, decimal, index, integer, pgEnum, pgTable, text, uuid } from "drizzle-orm/pg-core";
import { createSelectSchema } from "drizzle-zod";

import { taskPeriods } from "~/common/schemas";

import { id, timestamps } from "./common";
import { users } from "./user";

export const taskPeriod = pgEnum("task_period", taskPeriods);

export const tasks = pgTable(
  "tasks",
  {
    id: id(),
    userId: uuid()
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    title: text().notNull(),
    period: taskPeriod().notNull(),
    description: text(),
    amount: decimal({ precision: 15, scale: 4 }),
    dueDay: integer(),
    isArchived: boolean().notNull().default(false),
    sendNotifications: boolean().notNull().default(false),
    ...timestamps,
  },
  (table) => [
    index("idx_tasks_user_id").on(table.userId),
    index("idx_tasks_period").on(table.period),
    index("idx_tasks_archived").on(table.isArchived),
  ]
);

export const taskSelectSchema = createSelectSchema(tasks);
