CREATE TYPE "public"."budget_period" AS ENUM('week', 'month', 'quarter', 'year');
--> statement-breakpoint
CREATE TYPE "public"."budget_type" AS ENUM('fixed', 'percentage');
--> statement-breakpoint
CREATE TABLE "budgets" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"period" "budget_period" NOT NULL,
	"type" "budget_type" NOT NULL,
	"value" numeric(15, 4) NOT NULL,
	"accounts" uuid[],
	"is_archived" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "budget_records" (
	"id" uuid PRIMARY KEY NOT NULL,
	"budget_id" uuid NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"planned_amount" numeric(15, 4) NOT NULL,
	"used_amount" numeric(15, 4) DEFAULT '0.0000' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "budgets" ADD CONSTRAINT "budgets_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "budget_records" ADD CONSTRAINT "budget_records_budget_id_budgets_id_fk" FOREIGN KEY ("budget_id") REFERENCES "public"."budgets"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
CREATE INDEX "idx_budgets_user_id" ON "budgets" USING btree ("user_id");
--> statement-breakpoint
CREATE INDEX "idx_budget_records_for_budget_on_period" ON "budget_records" USING btree ("budget_id","start_date","end_date");
--> statement-breakpoint
CREATE INDEX "idx_budget_records_period" ON "budget_records" USING btree ("start_date","end_date");
