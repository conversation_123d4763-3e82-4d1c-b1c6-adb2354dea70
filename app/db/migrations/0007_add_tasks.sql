CREATE TYPE "public"."task_period" AS ENUM('weekly', 'monthly', 'quarterly', 'yearly');
--> statement-breakpoint
CREATE TYPE "public"."task_status" AS ENUM('active', 'completed', 'skipped');
--> statement-breakpoint
CREATE TABLE "tasks" (
	"id" uuid PRIMARY KEY NOT NULL,
	"user_id" uuid NOT NULL,
	"title" text NOT NULL,
	"period" "task_period" NOT NULL,
	"description" text,
	"amount" numeric(15, 4),
	"due_day" integer,
	"is_archived" boolean DEFAULT false NOT NULL,
	"send_notifications" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "task_records" (
	"id" uuid PRIMARY KEY NOT NULL,
	"task_id" uuid NOT NULL,
	"start_date" date NOT NULL,
	"end_date" date NOT NULL,
	"status" "task_status" DEFAULT 'active' NOT NULL,
	"amount" numeric(15, 4),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "transactions" ADD COLUMN "task_record_id" uuid;
--> statement-breakpoint
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
ALTER TABLE "task_records" ADD CONSTRAINT "task_records_task_id_tasks_id_fk" FOREIGN KEY ("task_id") REFERENCES "public"."tasks"("id") ON DELETE cascade ON UPDATE no action;
--> statement-breakpoint
CREATE INDEX "idx_tasks_user_id" ON "tasks" USING btree ("user_id");
--> statement-breakpoint
CREATE INDEX "idx_tasks_period" ON "tasks" USING btree ("period");
--> statement-breakpoint
CREATE INDEX "idx_tasks_archived" ON "tasks" USING btree ("is_archived");
--> statement-breakpoint
CREATE INDEX "idx_task_records_task_id" ON "task_records" USING btree ("task_id");
--> statement-breakpoint
CREATE INDEX "idx_task_records_period" ON "task_records" USING btree ("start_date","end_date");
--> statement-breakpoint
CREATE INDEX "idx_task_records_status" ON "task_records" USING btree ("status");
--> statement-breakpoint
ALTER TABLE "transactions" ADD CONSTRAINT "transactions_task_record_id_task_records_id_fk" FOREIGN KEY ("task_record_id") REFERENCES "public"."task_records"("id") ON DELETE set null ON UPDATE no action;
--> statement-breakpoint
CREATE INDEX "idx_transactions_task_record_id" ON "transactions" USING btree ("task_record_id");
