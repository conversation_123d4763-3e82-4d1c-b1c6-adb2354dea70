import type { <PERSON>pp<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type {
  CreateCategoryRoute,
  DeleteCategoryRoute,
  GetCategoryRoute,
  ListCategoriesRoute,
  UpdateCategoryRoute,
} from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import {
  createCategory as createCategoryAction,
  deleteCategory as deleteCategoryAction,
  getCategoriesByUserId,
  getCategoryById,
  updateCategory as updateCategoryAction,
} from "./actions";
import { mapCategoryResponse } from "./mappers";

export const listCategories: AppRouteHandler<ListCategoriesRoute> = async (c) => {
  const user = c.get("user");
  const categories = await getCategoriesByUserId(user.id);
  return c.json(categories.map(mapCategoryResponse), StatusCodes.OK);
};

export const createCategory: AppRouteHandler<CreateCategoryRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const category = await createCategoryAction(user.id, data);
  return c.json(mapCategoryResponse(category), StatusCodes.CREATED);
};

export const getCategory: AppRouteHandler<GetCategoryRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const category = await getCategoryById(id, user.id);
  if (!category) {
    return c.json({ success: false, message: "Category not found" }, StatusCodes.NOT_FOUND);
  }

  return c.json(mapCategoryResponse(category), StatusCodes.OK);
};

export const updateCategory: AppRouteHandler<UpdateCategoryRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const category = await updateCategoryAction(id, user.id, data);
  return c.json(mapCategoryResponse(category), StatusCodes.OK);
};

export const deleteCategory: AppRouteHandler<DeleteCategoryRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  await deleteCategoryAction(id, user.id);
  return c.body(null, StatusCodes.NO_CONTENT);
};
