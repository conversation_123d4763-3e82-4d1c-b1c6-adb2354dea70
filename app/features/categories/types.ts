import type { categorySelectSchema } from "~/db/schemas";
import type { categoryCreateSchema, categoryResponseSchema, categoryUpdateSchema } from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

export type CreateCategoryData = z.infer<typeof categoryCreateSchema>;
export type UpdateCategoryData = z.infer<typeof categoryUpdateSchema>;
export type Category = z4.infer<typeof categorySelectSchema>;
export type CategoryResponse = z.infer<typeof categoryResponseSchema>;
