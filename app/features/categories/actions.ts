import type { Category, CreateCategoryData, UpdateCategoryData } from "./types";

import { and, eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { categories } from "~/db/schemas";
import { StatusCodes } from "~/lib/status-codes";

export const createCategory = async (userId: string, data: CreateCategoryData): Promise<Category> => {
  const [category] = await db
    .insert(categories)
    .values({
      userId,
      ...data,
    })
    .returning();

  if (!category) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create category" });
  }

  return category;
};

export const getCategoriesByUserId = async (userId: string): Promise<Category[]> => {
  return await db.select().from(categories).where(eq(categories.userId, userId));
};

export const getCategoryById = async (id: string, userId: string): Promise<Category | null> => {
  const [category] = await db
    .select()
    .from(categories)
    .where(and(eq(categories.id, id), eq(categories.userId, userId)));

  return category ?? null;
};

export const updateCategory = async (id: string, userId: string, data: UpdateCategoryData): Promise<Category> => {
  const [category] = await db
    .update(categories)
    .set(data)
    .where(and(eq(categories.id, id), eq(categories.userId, userId)))
    .returning();

  if (!category) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Category not found" });
  }

  return category;
};

export const deleteCategory = async (id: string, userId: string): Promise<void> => {
  await db.delete(categories).where(and(eq(categories.id, id), eq(categories.userId, userId)));
};
