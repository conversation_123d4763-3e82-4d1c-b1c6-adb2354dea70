import createRouter from "~/lib/router";

import * as handlers from "./handlers";
import * as routes from "./routes";

const router = createRouter()
  .openapi(routes.listCategories, handlers.listCategories)
  .openapi(routes.createCategory, handlers.createCategory)
  .openapi(routes.getCategory, handlers.getCategory)
  .openapi(routes.updateCategory, handlers.updateCategory)
  .openapi(routes.deleteCategory, handlers.deleteCategory);

export default router;
