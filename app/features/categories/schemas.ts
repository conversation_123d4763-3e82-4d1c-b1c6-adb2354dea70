import { z } from "@hono/zod-openapi";

export const categoryCreateSchema = z
  .object({
    name: z.string().min(1).max(100).openapi({ example: "Food & Dining" }),
    color: z.string().nullish().default("#ff6b6b").openapi({ example: "#FF6B6B" }),
    icon: z.string().nullish().openapi({ example: "restaurant" }),
    isExpense: z.boolean().default(true).openapi({ example: true }),
  })
  .openapi("CategoryCreateRequest");

export const categoryUpdateSchema = z
  .object({
    name: z.string().min(1).max(100).optional().openapi({ example: "Food & Dining" }),
    color: z.string().optional().openapi({ example: "#FF6B6B" }),
    icon: z.string().optional().openapi({ example: "restaurant" }),
    isExpense: z.boolean().optional().openapi({ example: true }),
  })
  .openapi("CategoryUpdateRequest");

export const categoryResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().openapi({ example: "Food & Dining" }),
    color: z.string().nullable().openapi({ example: "#FF6B6B" }),
    icon: z.string().nullable().openapi({ example: "restaurant" }),
    isExpense: z.boolean().openapi({ example: true }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("Category");
