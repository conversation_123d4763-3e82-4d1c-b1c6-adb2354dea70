import { createRoute, z } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import { categoryCreateSchema, categoryResponseSchema, categoryUpdateSchema } from "./schemas";

const tags = ["Categories"];

export const listCategories = createRoute({
  tags,
  path: "/categories",
  method: "get",
  operationId: "listCategories",
  summary: "List categories",
  description: "Get all categories for the current user",
  security: [{ JWT: [] }],
  responses: {
    [StatusCodes.OK]: jsonContent(z.array(categoryResponseSchema), "Categories retrieved"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const createCategory = createRoute({
  tags,
  path: "/categories",
  method: "post",
  operationId: "createCategory",
  summary: "Create category",
  description: "Create a new category",
  security: [{ JWT: [] }],
  request: {
    body: requestBody(categoryCreateSchema, "Category to create"),
  },
  responses: {
    [StatusCodes.CREATED]: jsonContent(categoryResponseSchema, "Category created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const getCategory = createRoute({
  tags,
  path: "/categories/{id}",
  method: "get",
  operationId: "getCategory",
  summary: "Get category",
  description: "Get category by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(categoryResponseSchema, "Category retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Category not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const updateCategory = createRoute({
  tags,
  path: "/categories/{id}",
  method: "put",
  operationId: "updateCategory",
  summary: "Update category",
  description: "Update category by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(categoryUpdateSchema, "Category data to update"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(categoryResponseSchema, "Category updated"),
    [StatusCodes.NOT_FOUND]: errorSchema("Category not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const deleteCategory = createRoute({
  tags,
  path: "/categories/{id}",
  method: "delete",
  operationId: "deleteCategory",
  summary: "Delete category",
  description: "Delete category by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.NO_CONTENT]: {
      description: "Category deleted",
    },
    [StatusCodes.NOT_FOUND]: errorSchema("Category not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

// Route Types
export type ListCategoriesRoute = typeof listCategories;
export type CreateCategoryRoute = typeof createCategory;
export type GetCategoryRoute = typeof getCategory;
export type UpdateCategoryRoute = typeof updateCategory;
export type DeleteCategoryRoute = typeof deleteCategory;
