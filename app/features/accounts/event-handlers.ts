import type { Events, TransactionCreatedEvent, TransactionDeletedEvent, TransactionUpdatedEvent } from "~/events/types";
import type { AppBindings } from "~/types";

import { defineHandler } from "@hono/event-emitter";

import { recalculateAccountBalance } from "./actions";

export const transactionCreated = defineHandler<Events, typeof TransactionCreatedEvent, AppBindings>(
  async (c, payload) => {
    const user = c.get("user");
    await recalculateAccountBalance(payload.transaction.accountId, user);
    if (payload.transaction.accountToId) {
      await recalculateAccountBalance(payload.transaction.accountToId, user);
    }
  }
);

export const transactionUpdated = defineHandler<Events, typeof TransactionUpdatedEvent, AppBindings>(
  async (c, payload) => {
    const user = c.get("user");
    const accounts = new Set<string>(
      [
        payload.previousTransaction.accountId,
        payload.previousTransaction.accountToId,
        payload.transaction.accountId,
        payload.transaction.accountToId,
      ].filter((accountId) => accountId !== null)
    );

    for (const accountId of accounts) {
      await recalculateAccountBalance(accountId, user);
    }
  }
);

export const transactionDeleted = defineHandler<Events, typeof TransactionDeletedEvent, AppBindings>(
  async (c, payload) => {
    const user = c.get("user");
    await recalculateAccountBalance(payload.transaction.accountId, user);
    if (payload.transaction.accountToId) {
      await recalculateAccountBalance(payload.transaction.accountToId, user);
    }
  }
);
