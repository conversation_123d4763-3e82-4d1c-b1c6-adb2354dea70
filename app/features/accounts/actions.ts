import type { AccountUpdateValues } from "~/db/types";
import type { User } from "~/features/users/types";
import type {
  AccountGroup,
  AccountWithGroup,
  CreateAccountData,
  CreateAccountGroupData,
  UpdateAccountBalanceData,
  UpdateAccountData,
  UpdateAccountGroupData,
} from "./types";

import Decimal from "decimal.js";
import { and, eq, sum } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { accountGroups, accounts, transactions } from "~/db/schemas";
import { convertAmount } from "~/features/currencies/actions";
import { StatusCodes } from "~/lib/status-codes";

// Account Group Actions
export const createAccountGroup = async (userId: string, data: CreateAccountGroupData): Promise<AccountGroup> => {
  const [accountGroup] = await db
    .insert(accountGroups)
    .values({
      userId,
      ...data,
    })
    .returning();

  if (!accountGroup) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create account group" });
  }

  return accountGroup;
};

export const getAccountGroupsByUserId = async (userId: string): Promise<AccountGroup[]> => {
  return await db.select().from(accountGroups).where(eq(accountGroups.userId, userId));
};

export const getAccountGroupById = async (id: string, userId: string): Promise<AccountGroup | undefined> => {
  const [accountGroup] = await db
    .select()
    .from(accountGroups)
    .where(and(eq(accountGroups.id, id), eq(accountGroups.userId, userId)));

  return accountGroup;
};

export const updateAccountGroup = async (
  id: string,
  userId: string,
  data: UpdateAccountGroupData
): Promise<AccountGroup> => {
  const [accountGroup] = await db
    .update(accountGroups)
    .set(data)
    .where(and(eq(accountGroups.id, id), eq(accountGroups.userId, userId)))
    .returning();

  if (!accountGroup) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account group not found" });
  }

  return accountGroup;
};

export const deleteAccountGroup = async (id: string, userId: string): Promise<void> => {
  await db.delete(accountGroups).where(and(eq(accountGroups.id, id), eq(accountGroups.userId, userId)));
};

// Account Actions
export const createAccount = async (user: User, data: CreateAccountData): Promise<AccountWithGroup> => {
  // Validate group exists if provided
  if (data.groupId) {
    const group = await getAccountGroupById(data.groupId, user.id);
    if (!group) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account group not found" });
    }
  }

  // Calculate base currency amounts
  const baseOpeningBalance = await convertAmount(data.openingBalance, data.currency, user.baseCurrency);
  const baseCurrentBalance = baseOpeningBalance; // Initially same as opening balance

  const [result] = await db
    .insert(accounts)
    .values({
      userId: user.id,
      ...data,
      currentBalance: data.openingBalance, // Initially same as opening balance
      baseOpeningBalance,
      baseCurrentBalance,
    })
    .returning({ id: accounts.id });

  if (!result) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create account" });
  }

  const account = await getAccountById(result.id, user.id);
  return account!;
};

export const getAccountsByUserId = async (userId: string): Promise<AccountWithGroup[]> => {
  return await db.query.accounts.findMany({
    where: eq(accounts.userId, userId),
    with: {
      group: true,
    },
  });
};

export const getAccountById = async (id: string, userId: string): Promise<AccountWithGroup | undefined> => {
  const account = await db.query.accounts.findFirst({
    where: and(eq(accounts.id, id), eq(accounts.userId, userId)),
    with: {
      group: true,
    },
  });

  return account;
};

export const updateAccount = async (id: string, user: User, data: UpdateAccountData): Promise<AccountWithGroup> => {
  // Validate group exists if provided

  if (data.groupId) {
    const group = await getAccountGroupById(data.groupId, user.id);
    if (!group) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account group not found" });
    }
  }

  // Get current account to check if currency changed
  const currentAccount = await getAccountById(id, user.id);
  if (!currentAccount) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  let updateData: AccountUpdateValues = { ...data };

  // If currency changed, recalculate base amounts
  if (data.currency && data.currency !== currentAccount.currency) {
    const baseOpeningBalance = await convertAmount(currentAccount.openingBalance, data.currency, user.baseCurrency);
    const baseCurrentBalance = await convertAmount(currentAccount.currentBalance, data.currency, user.baseCurrency);

    updateData = {
      ...updateData,
      baseOpeningBalance,
      baseCurrentBalance,
    };
  }

  const [result] = await db
    .update(accounts)
    .set(updateData)
    .where(and(eq(accounts.id, id), eq(accounts.userId, user.id)))
    .returning({ id: accounts.id });

  if (!result) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  const account = await getAccountById(result.id, user.id);

  return account!;
};

export const updateAccountBalance = async (
  id: string,
  user: User,
  data: UpdateAccountBalanceData
): Promise<AccountWithGroup> => {
  // Get current account
  const currentAccount = await getAccountById(id, user.id);
  if (!currentAccount) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  // Calculate base current balance
  const baseCurrentBalance = await convertAmount(data.currentBalance, currentAccount.currency, user.baseCurrency);

  const [result] = await db
    .update(accounts)
    .set({
      currentBalance: data.currentBalance,
      baseCurrentBalance,
    })
    .where(and(eq(accounts.id, id), eq(accounts.userId, user.id)))
    .returning({ id: accounts.id });

  if (!result) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  const account = await getAccountById(result.id, user.id);

  return account!;
};

export const deleteAccount = async (id: string, userId: string): Promise<void> => {
  await db.delete(accounts).where(and(eq(accounts.id, id), eq(accounts.userId, userId)));
};

/**
 * Recalculates the current balance for an account based on all related transactions
 *
 * Balance Calculation Logic:
 * 1. Start with the account's opening balance
 * 2. Use database aggregation to calculate:
 *    - Total income (money coming in)
 *    - Total expenses (money going out)
 *    - Total outgoing transfers (money leaving)
 *    - Total incoming transfers (money arriving)
 * 3. Calculate: opening_balance + income - expenses - outgoing_transfers + incoming_transfers
 * 4. Update both current balance and base current balance
 *
 * @param accountId - The account ID to recalculate balance for
 * @param user - The user object (for currency conversion and validation)
 * @returns The updated account's current balance
 */
export const recalculateAccountBalance = async (accountId: string, user: User): Promise<string> => {
  // Get the account and validate ownership
  const account = await getAccountById(accountId, user.id);
  if (!account) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Account not found" });
  }

  // Start with opening balance using Decimal for precision
  let currentBalance = new Decimal(account.openingBalance);

  // Calculate total income for this account
  const incomeResult = await db
    .select({ total: sum(transactions.amount) })
    .from(transactions)
    .where(
      and(eq(transactions.userId, user.id), eq(transactions.accountId, accountId), eq(transactions.type, "income"))
    );
  const totalIncome = new Decimal(incomeResult[0]?.total ?? "0");

  // Calculate total expenses for this account
  const expenseResult = await db
    .select({ total: sum(transactions.amount) })
    .from(transactions)
    .where(
      and(eq(transactions.userId, user.id), eq(transactions.accountId, accountId), eq(transactions.type, "expense"))
    );
  const totalExpenses = new Decimal(expenseResult[0]?.total ?? "0");

  // Calculate total outgoing transfers from this account
  const outgoingTransferResult = await db
    .select({ total: sum(transactions.amount) })
    .from(transactions)
    .where(
      and(eq(transactions.userId, user.id), eq(transactions.accountId, accountId), eq(transactions.type, "transfer"))
    );
  const totalOutgoingTransfers = new Decimal(outgoingTransferResult[0]?.total ?? "0");

  // Calculate total incoming transfers to this account
  const incomingTransferResult = await db
    .select({ total: sum(transactions.amountTo) })
    .from(transactions)
    .where(
      and(eq(transactions.userId, user.id), eq(transactions.accountToId, accountId), eq(transactions.type, "transfer"))
    );
  const totalIncomingTransfers = new Decimal(incomingTransferResult[0]?.total ?? "0");

  // Calculate final balance: opening + income - expenses - outgoing_transfers + incoming_transfers
  currentBalance = currentBalance
    .plus(totalIncome)
    .minus(totalExpenses)
    .minus(totalOutgoingTransfers)
    .plus(totalIncomingTransfers);

  // Convert to string with 4 decimal places
  const newCurrentBalance = currentBalance.toFixed(4);

  // Calculate base current balance
  const baseCurrentBalance = await convertAmount(newCurrentBalance, account.currency, user.baseCurrency);

  // Update the account balance and return the updated values
  const [updatedAccount] = await db
    .update(accounts)
    .set({
      currentBalance: newCurrentBalance,
      baseCurrentBalance,
    })
    .where(and(eq(accounts.id, accountId), eq(accounts.userId, user.id)))
    .returning({ id: accounts.id, currentBalance: accounts.currentBalance });

  if (!updatedAccount) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to update account balance" });
  }

  // Return the updated account's current balance
  return newCurrentBalance;
};
