import { createRoute, z } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import {
  taskCreateSchema,
  taskHistoryQuerySchema,
  taskHistoryResponseSchema,
  taskListQuerySchema,
  taskListResponseSchema,
  taskResponseSchema,
  taskStatusUpdateSchema,
  taskTransactionAssignmentSchema,
  taskUpdateSchema,
} from "./schemas";

const tags = ["Tasks"];

// Task CRUD Routes
export const listTasks = createRoute({
  tags,
  path: "/tasks",
  method: "get",
  operationId: "listTasks",
  summary: "List tasks",
  description: "Get all active tasks for the current user",
  security: [{ JWT: [] }],
  request: {
    query: taskListQuerySchema,
  },
  responses: {
    [StatusCodes.OK]: jsonContent(taskListResponseSchema, "Tasks retrieved"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const createTask = createRoute({
  tags,
  path: "/tasks",
  method: "post",
  operationId: "createTask",
  summary: "Create task",
  description: "Create a new recurring task",
  security: [{ JWT: [] }],
  request: {
    body: requestBody(taskCreateSchema, "Task to create"),
  },
  responses: {
    [StatusCodes.CREATED]: jsonContent(taskResponseSchema, "Task created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const getTask = createRoute({
  tags,
  path: "/tasks/{id}",
  method: "get",
  operationId: "getTask",
  summary: "Get task",
  description: "Get task by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(taskResponseSchema, "Task retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const updateTask = createRoute({
  tags,
  path: "/tasks/{id}",
  method: "put",
  operationId: "updateTask",
  summary: "Update task",
  description: "Update task by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(taskUpdateSchema, "Task data to update"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(taskResponseSchema, "Task updated"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const updateTaskStatus = createRoute({
  tags,
  path: "/tasks/{id}/status",
  method: "put",
  operationId: "updateTaskStatus",
  summary: "Update task status",
  description: "Update the status of the current active task record for the specified task",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(taskStatusUpdateSchema, "Task status update data"),
  },
  responses: {
    [StatusCodes.OK]: { description: "Task status updated" },
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const deleteTask = createRoute({
  tags,
  path: "/tasks/{id}",
  method: "delete",
  operationId: "deleteTask",
  summary: "Delete task",
  description: "Delete task by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.NO_CONTENT]: { description: "Task deleted" },
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

// Task History Route
export const getTaskHistory = createRoute({
  tags,
  path: "/tasks/{id}/history",
  method: "get",
  operationId: "getTaskHistory",
  summary: "Get task history",
  description: "Get paginated history of task records for a task",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    query: taskHistoryQuerySchema,
  },
  responses: {
    [StatusCodes.OK]: jsonContent(taskHistoryResponseSchema, "Task history retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

// Transaction Assignment Routes
export const assignTaskTransactions = createRoute({
  tags,
  path: "/tasks/{id}/transactions",
  method: "post",
  operationId: "assignTaskTransactions",
  summary: "Assign transactions to task",
  description: "Assign transactions to the current task record",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(taskTransactionAssignmentSchema, "Transactions to assign"),
  },
  responses: {
    [StatusCodes.OK]: { description: "Transactions assigned" },
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const replaceTaskTransactions = createRoute({
  tags,
  path: "/tasks/{id}/transactions",
  method: "put",
  operationId: "replaceTaskTransactions",
  summary: "Replace task transactions",
  description: "Replace all assigned transactions for the current task record",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(taskTransactionAssignmentSchema, "Transactions to replace with"),
  },
  responses: {
    [StatusCodes.OK]: { description: "Transactions replaced" },
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const removeTaskTransactions = createRoute({
  tags,
  path: "/tasks/{id}/transactions",
  method: "delete",
  operationId: "removeTaskTransactions",
  summary: "Remove task transactions",
  description: "Remove specified transactions from the current task record",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(taskTransactionAssignmentSchema, "Transactions to remove"),
  },
  responses: {
    [StatusCodes.OK]: { description: "Transactions removed" },
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.NOT_FOUND]: errorSchema("Task not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

// Route Types
export type ListTasksRoute = typeof listTasks;
export type CreateTaskRoute = typeof createTask;
export type GetTaskRoute = typeof getTask;
export type UpdateTaskRoute = typeof updateTask;
export type UpdateTaskStatusRoute = typeof updateTaskStatus;
export type DeleteTaskRoute = typeof deleteTask;
export type GetTaskHistoryRoute = typeof getTaskHistory;
export type AssignTaskTransactionsRoute = typeof assignTaskTransactions;
export type ReplaceTaskTransactionsRoute = typeof replaceTaskTransactions;
export type RemoveTaskTransactionsRoute = typeof removeTaskTransactions;
