import { z } from "@hono/zod-openapi";

import { decimal, paginationMeta, paginationQuery, taskPeriod, taskStatus } from "~/common/schemas";

// Task Schemas
export const taskCreateSchema = z
  .object({
    title: z.string().min(1).max(100).openapi({ example: "Pay Rent" }),
    period: taskPeriod("Task recurrence period"),
    description: z.string().nullish().openapi({ example: "Monthly rent payment" }),
    amount: decimal().nullish().openapi({ example: "1200.0000" }),
    dueDay: z
      .number()
      .int()
      .min(1)
      .max(31)
      .nullish()
      .openapi({ example: 1, description: "Day of the period when task is due" }),
    sendNotifications: z.boolean().default(false).openapi({ example: false }),
  })
  .openapi("TaskCreateRequest");

export const taskUpdateSchema = z
  .object({
    title: z.string().min(1).max(100).optional().openapi({ example: "Pay Rent" }),
    period: taskPeriod("Task recurrence period").optional(),
    description: z.string().optional().openapi({ example: "Monthly rent payment" }),
    amount: decimal().optional().openapi({ example: "1200.0000" }),
    dueDay: z
      .number()
      .int()
      .min(1)
      .max(31)
      .optional()
      .openapi({ example: 1, description: "Day of the period when task is due" }),
    isArchived: z.boolean().optional().openapi({ example: false }),
    sendNotifications: z.boolean().optional().openapi({ example: false }),
  })
  .openapi("TaskUpdateRequest");

export const taskResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    title: z.string().openapi({ example: "Pay Rent" }),
    period: taskPeriod("Task recurrence period"),
    description: z.string().nullable().openapi({ example: "Monthly rent payment" }),
    amount: decimal().nullable().openapi({ example: "1200.0000" }),
    dueDay: z.number().int().nullable().openapi({ example: 1 }),
    isArchived: z.boolean().openapi({ example: false }),
    sendNotifications: z.boolean().openapi({ example: false }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("TaskResponse");

// Task Record Schemas
export const taskRecordUpdateSchema = z
  .object({
    status: taskStatus("Task record status"),
  })
  .openapi("TaskRecordUpdateRequest");

export const taskRecordResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    taskId: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    startDate: z.string().date().openapi({ example: "2024-01-01" }),
    endDate: z.string().date().openapi({ example: "2024-01-31" }),
    status: taskStatus("Task record status"),
    amount: decimal().nullable().openapi({ example: "1200.0000" }),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("TaskRecordResponse");

// List Schemas
export const taskListQuerySchema = paginationQuery().openapi("TaskListQuery");

export const taskListResponseSchema = z
  .object({
    items: z.array(taskResponseSchema),
    meta: paginationMeta(),
  })
  .openapi("TaskListResponse");

export const taskHistoryQuerySchema = paginationQuery().openapi("TaskHistoryQuery");

export const taskHistoryResponseSchema = z
  .object({
    items: z.array(taskRecordResponseSchema),
    meta: paginationMeta(),
  })
  .openapi("TaskHistoryResponse");

// Transaction Assignment Schemas
export const taskTransactionAssignmentSchema = z
  .object({
    transactions: z.array(z.string().uuid()).openapi({ example: ["123e4567-e89b-12d3-a456-************"] }),
  })
  .openapi("TaskTransactionAssignment");
