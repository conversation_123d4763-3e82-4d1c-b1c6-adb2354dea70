import type { taskRecordSelectSchema, taskSelectSchema } from "~/db/schemas";
import type {
  taskCreateSchema,
  taskHistoryQuerySchema,
  taskListQuerySchema,
  taskListResponseSchema,
  taskRecordResponseSchema,
  taskRecordUpdateSchema,
  taskResponseSchema,
  taskStatusUpdateSchema,
  taskTransactionAssignmentSchema,
  taskUpdateSchema,
} from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

// Task Types
export type CreateTaskData = z.infer<typeof taskCreateSchema>;
export type UpdateTaskData = z.infer<typeof taskUpdateSchema>;
export type TaskResponse = z.infer<typeof taskResponseSchema>;
export type TaskListQuery = z.infer<typeof taskListQuerySchema>;
export type TaskListResponse = z.infer<typeof taskListResponseSchema>;

// Task Record Types
export type UpdateTaskRecordData = z.infer<typeof taskRecordUpdateSchema>;
export type UpdateTaskStatusData = z.infer<typeof taskStatusUpdateSchema>;
export type TaskRecord = z4.infer<typeof taskRecordSelectSchema>;
export type TaskRecordResponse = z.infer<typeof taskRecordResponseSchema>;
export type TaskHistoryQuery = z.infer<typeof taskHistoryQuerySchema>;

// Task with current record
export type Task = z4.infer<typeof taskSelectSchema> & {
  current?: TaskRecord | null;
};

// Transaction Assignment Types
export type TaskTransactionAssignment = z.infer<typeof taskTransactionAssignmentSchema>;

// Task Period Types
export type TaskPeriod = "weekly" | "monthly" | "quarterly" | "yearly";

// Task Status Types
export type TaskStatus = "active" | "completed" | "skipped";

// Period Dates Type
export type PeriodDates = {
  startDate: string;
  endDate: string;
};
