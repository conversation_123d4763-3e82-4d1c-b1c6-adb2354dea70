import type { Task, TaskRecord, TaskRecordResponse, TaskResponse } from "./types";

export const mapTaskResponse = (task: Task): TaskResponse => ({
  id: task.id,
  title: task.title,
  period: task.period,
  description: task.description,
  amount: task.amount,
  dueDay: task.dueDay,
  isArchived: task.isArchived,
  sendNotifications: task.sendNotifications,
  createdAt: task.createdAt.toISOString(),
  updatedAt: task.updatedAt.toISOString(),
});

export const mapTaskRecordResponse = (taskRecord: TaskRecord): TaskRecordResponse => ({
  id: taskRecord.id,
  taskId: taskRecord.taskId,
  startDate: taskRecord.startDate,
  endDate: taskRecord.endDate,
  status: taskRecord.status,
  amount: taskRecord.amount,
  createdAt: taskRecord.createdAt.toISOString(),
  updatedAt: taskRecord.updatedAt.toISOString(),
});
