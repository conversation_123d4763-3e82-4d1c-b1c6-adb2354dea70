// Example test file to demonstrate the tasks feature
// This is not a real test file, just an example of how the API would be used

import type { TaskListResponse, TaskResponse } from "./types";

// Example API usage:

// 1. Create a new task
const createTaskExample = {
  title: "Pay Rent",
  period: "monthly" as const,
  description: "Monthly rent payment",
  amount: "1200.0000",
  dueDay: 1,
  sendNotifications: false,
};

// 2. List tasks with pagination
const listTasksExample = {
  page: 1,
  limit: 20,
};

// 3. Update a task
const updateTaskExample = {
  title: "Pay Monthly Rent",
  amount: "1250.0000",
  isArchived: false,
};

// 4. Assign transactions to a task
const assignTransactionsExample = {
  transactions: ["123e4567-e89b-12d3-a456-426614174000"],
};

// Example response types:
const taskResponseExample: TaskResponse = {
  id: "123e4567-e89b-12d3-a456-426614174000",
  title: "Pay Rent",
  period: "monthly",
  description: "Monthly rent payment",
  amount: "1200.0000",
  dueDay: 1,
  isArchived: false,
  sendNotifications: false,
  createdAt: "2024-01-01T00:00:00.000Z",
  updatedAt: "2024-01-01T00:00:00.000Z",
};

const taskListResponseExample: TaskListResponse = {
  items: [taskResponseExample],
  meta: {
    total: 1,
    page: 1,
    limit: 20,
    count: 1,
  },
};

// Example API endpoints:
// POST /api/v1/tasks - Create task
// GET /api/v1/tasks - List tasks
// GET /api/v1/tasks/{id} - Get task
// PUT /api/v1/tasks/{id} - Update task
// DELETE /api/v1/tasks/{id} - Delete task
// GET /api/v1/tasks/{id}/history - Get task history
// POST /api/v1/tasks/{id}/transactions - Assign transactions
// PUT /api/v1/tasks/{id}/transactions - Replace transactions
// DELETE /api/v1/tasks/{id}/transactions - Remove transactions

export {
  createTaskExample,
  listTasksExample,
  updateTaskExample,
  assignTransactionsExample,
  taskResponseExample,
  taskListResponseExample,
};
