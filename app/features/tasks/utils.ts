import type { PeriodDates, TaskPeriod } from "./types";

import {
  addMonths,
  addQuarters,
  addWeeks,
  addYears,
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  formatISO,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
} from "date-fns";

/**
 * Calculate the start and end dates for a task period containing the given date
 */
export const getPeriodDates = (period: TaskPeriod, date: Date | string = new Date()): PeriodDates => {
  const targetDate = typeof date === "string" ? new Date(date) : date;

  switch (period) {
    case "weekly":
      return {
        startDate: formatISO(startOfWeek(targetDate, { weekStartsOn: 1 }), { representation: "date" }), // Monday start
        endDate: formatISO(endOfWeek(targetDate, { weekStartsOn: 1 }), { representation: "date" }),
      };
    case "monthly":
      return {
        startDate: formatISO(startOfMonth(targetDate), { representation: "date" }),
        endDate: formatISO(endOfMonth(targetDate), { representation: "date" }),
      };
    case "quarterly":
      return {
        startDate: formatISO(startOfQuarter(targetDate), { representation: "date" }),
        endDate: formatISO(endOfQuarter(targetDate), { representation: "date" }),
      };
    case "yearly":
      return {
        startDate: formatISO(startOfYear(targetDate), { representation: "date" }),
        endDate: formatISO(endOfYear(targetDate), { representation: "date" }),
      };
    default:
      throw new Error(`Unsupported period: ${period as string}`);
  }
};

/**
 * Calculate the next period dates after the given period
 */
export const getNextPeriodDates = (period: TaskPeriod, currentStartDate: string): PeriodDates => {
  const currentDate = new Date(currentStartDate);

  let nextDate: Date;
  switch (period) {
    case "weekly":
      nextDate = addWeeks(currentDate, 1);
      break;
    case "monthly":
      nextDate = addMonths(currentDate, 1);
      break;
    case "quarterly":
      nextDate = addQuarters(currentDate, 1);
      break;
    case "yearly":
      nextDate = addYears(currentDate, 1);
      break;
    default:
      throw new Error(`Unsupported period: ${period as string}`);
  }

  return getPeriodDates(period, nextDate);
};

/**
 * Check if a date falls within a period
 */
export const isDateInPeriod = (date: string, startDate: string, endDate: string): boolean => {
  const targetDate = new Date(date);
  const start = new Date(startDate);
  const end = new Date(endDate);

  return targetDate >= start && targetDate <= end;
};

/**
 * Format period dates for display
 */
export const formatPeriod = (period: TaskPeriod, date: Date = new Date()): string => {
  const { startDate, endDate } = getPeriodDates(period, date);
  switch (period) {
    case "weekly":
      return `Week of ${new Date(startDate).toLocaleDateString()}`;
    case "monthly":
      return new Date(startDate).toLocaleDateString("en-US", { year: "numeric", month: "long" });
    case "quarterly":
      return `Q${Math.floor(new Date(startDate).getMonth() / 3) + 1} ${new Date(startDate).getFullYear()}`;
    case "yearly":
      return new Date(startDate).getFullYear().toString();
    default:
      return `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`;
  }
};

/**
 * Get the current active task record period for a task
 */
export const getCurrentTaskRecordPeriod = (period: TaskPeriod, date: Date = new Date()): PeriodDates => {
  return getPeriodDates(period, date);
};
