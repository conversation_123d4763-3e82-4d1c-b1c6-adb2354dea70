import type {
  CreateTaskData,
  Task,
  TaskHistoryQuery,
  TaskListQuery,
  TaskRecord,
  TaskTransactionAssignment,
  UpdateTaskData,
  UpdateTaskRecordData,
} from "./types";

import Decimal from "decimal.js";
import { and, desc, eq, sum } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { taskRecords, tasks, transactions } from "~/db/schemas";
import { StatusCodes } from "~/lib/status-codes";

import { getCurrentTaskRecordPeriod, isDateInPeriod } from "./utils";

// Task CRUD Operations
export const createTask = async (userId: string, data: CreateTaskData): Promise<Task> => {
  const [task] = await db
    .insert(tasks)
    .values({ userId, ...data })
    .returning();

  if (!task) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create task" });
  }

  return task;
};

export const getTasksByUserId = async (
  userId: string,
  query: TaskListQuery
): Promise<{ items: Task[]; total: number }> => {
  const { page, limit } = query;
  const offset = (page - 1) * limit;

  // Get total count (excluding archived tasks by default)
  const total = await db.$count(tasks, and(eq(tasks.userId, userId), eq(tasks.isArchived, false)));

  // Get paginated tasks
  const items = await db
    .select()
    .from(tasks)
    .where(and(eq(tasks.userId, userId), eq(tasks.isArchived, false)))
    .orderBy(desc(tasks.createdAt))
    .limit(limit)
    .offset(offset);

  return { items, total };
};

export const getTaskById = async (id: string, userId: string): Promise<Task | null> => {
  const [task] = await db
    .select()
    .from(tasks)
    .where(and(eq(tasks.id, id), eq(tasks.userId, userId)));

  return task ?? null;
};

export const updateTask = async (id: string, userId: string, data: UpdateTaskData): Promise<Task> => {
  const [task] = await db
    .update(tasks)
    .set(data)
    .where(and(eq(tasks.id, id), eq(tasks.userId, userId)))
    .returning();

  if (!task) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task not found" });
  }

  return task;
};

export const deleteTask = async (id: string, userId: string): Promise<void> => {
  // This will cascade delete all task records due to foreign key constraint
  await db.delete(tasks).where(and(eq(tasks.id, id), eq(tasks.userId, userId)));
};

// Task Record Operations
export const getTaskRecordsByTaskId = async (
  taskId: string,
  userId: string,
  query: TaskHistoryQuery
): Promise<{ items: TaskRecord[]; total: number }> => {
  const { page, limit } = query;
  const offset = (page - 1) * limit;

  // Verify task belongs to user
  const task = await getTaskById(taskId, userId);
  if (!task) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task not found" });
  }

  // Get total count
  const total = await db.$count(taskRecords, eq(taskRecords.taskId, taskId));

  // Get paginated task records
  const items = await db
    .select()
    .from(taskRecords)
    .where(eq(taskRecords.taskId, taskId))
    .orderBy(desc(taskRecords.startDate))
    .limit(limit)
    .offset(offset);

  return { items, total };
};

export const getCurrentTaskRecord = async (taskId: string): Promise<TaskRecord | null> => {
  const task = await db.query.tasks.findFirst({
    where: eq(tasks.id, taskId),
  });

  if (!task) {
    return null;
  }

  const currentPeriod = getCurrentTaskRecordPeriod(task.period);

  const [taskRecord] = await db
    .select()
    .from(taskRecords)
    .where(
      and(
        eq(taskRecords.taskId, taskId),
        eq(taskRecords.startDate, currentPeriod.startDate),
        eq(taskRecords.endDate, currentPeriod.endDate)
      )
    );

  return taskRecord ?? null;
};

export const createTaskRecord = async (taskId: string, startDate: string, endDate: string): Promise<TaskRecord> => {
  const task = await db.query.tasks.findFirst({
    where: eq(tasks.id, taskId),
  });

  if (!task) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task not found" });
  }

  const [taskRecord] = await db
    .insert(taskRecords)
    .values({
      taskId,
      startDate,
      endDate,
      amount: task.amount,
    })
    .returning();

  if (!taskRecord) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create task record" });
  }

  return taskRecord;
};

export const updateTaskRecordStatus = async (
  taskRecordId: string,
  userId: string,
  data: UpdateTaskRecordData
): Promise<TaskRecord> => {
  // Verify task record belongs to user's task
  const taskRecord = await db.query.taskRecords.findFirst({
    where: eq(taskRecords.id, taskRecordId),
    with: {
      task: true,
    },
  });

  if (!taskRecord || taskRecord.task.userId !== userId) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task record not found" });
  }

  const [updatedTaskRecord] = await db
    .update(taskRecords)
    .set({ status: data.status })
    .where(eq(taskRecords.id, taskRecordId))
    .returning();

  if (!updatedTaskRecord) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to update task record" });
  }

  return updatedTaskRecord;
};

// Task Record Auto-creation
export const createTaskRecordsForNewPeriods = async (): Promise<void> => {
  // Get all active (non-archived) tasks
  const activeTasks = await db.select().from(tasks).where(eq(tasks.isArchived, false));

  for (const task of activeTasks) {
    const currentPeriod = getCurrentTaskRecordPeriod(task.period);

    // Check if task record already exists for current period
    const existingRecord = await db.query.taskRecords.findFirst({
      where: and(
        eq(taskRecords.taskId, task.id),
        eq(taskRecords.startDate, currentPeriod.startDate),
        eq(taskRecords.endDate, currentPeriod.endDate)
      ),
    });

    if (!existingRecord) {
      await createTaskRecord(task.id, currentPeriod.startDate, currentPeriod.endDate);
    }
  }
};

// Transaction Assignment Operations
export const assignTransactionsToTaskRecord = async (
  taskId: string,
  userId: string,
  data: TaskTransactionAssignment
): Promise<void> => {
  const task = await getTaskById(taskId, userId);
  if (!task) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task not found" });
  }

  // Get current task record
  let currentTaskRecord = await getCurrentTaskRecord(taskId);
  if (!currentTaskRecord) {
    const currentPeriod = getCurrentTaskRecordPeriod(task.period);
    currentTaskRecord = await createTaskRecord(taskId, currentPeriod.startDate, currentPeriod.endDate);
  }

  // Validate transactions belong to user and dates fall within task record period
  for (const transactionId of data.transactions) {
    const transaction = await db.query.transactions.findFirst({
      where: and(eq(transactions.id, transactionId), eq(transactions.userId, userId)),
    });

    if (!transaction) {
      throw new HTTPException(StatusCodes.NOT_FOUND, { message: `Transaction ${transactionId} not found` });
    }

    if (!isDateInPeriod(transaction.transactionDate, currentTaskRecord.startDate, currentTaskRecord.endDate)) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: `Transaction ${transactionId} date does not fall within task record period`,
      });
    }
  }

  // Assign transactions to task record
  for (let i = 0; i < data.transactions.length; i++) {
    await db
      .update(transactions)
      .set({ taskRecordId: currentTaskRecord.id })
      .where(and(eq(transactions.userId, userId), eq(transactions.id, data.transactions[i]!)));
  }

  // Check for auto-completion
  await checkAndCompleteTaskRecord(currentTaskRecord.id);
};

export const replaceTransactionsInTaskRecord = async (
  taskId: string,
  userId: string,
  data: TaskTransactionAssignment
): Promise<void> => {
  const task = await getTaskById(taskId, userId);
  if (!task) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task not found" });
  }

  // Get current task record
  let currentTaskRecord = await getCurrentTaskRecord(taskId);
  if (!currentTaskRecord) {
    const currentPeriod = getCurrentTaskRecordPeriod(task.period);
    currentTaskRecord = await createTaskRecord(taskId, currentPeriod.startDate, currentPeriod.endDate);
  }

  // Remove all existing assignments for this task record
  await db
    .update(transactions)
    .set({ taskRecordId: null })
    .where(and(eq(transactions.userId, userId), eq(transactions.taskRecordId, currentTaskRecord.id)));

  // Assign new transactions
  if (data.transactions.length > 0) {
    await assignTransactionsToTaskRecord(taskId, userId, data);
  } else {
    // If no transactions, reset task record status to active
    await db.update(taskRecords).set({ status: "active" }).where(eq(taskRecords.id, currentTaskRecord.id));
  }
};

export const removeTransactionsFromTaskRecord = async (
  taskId: string,
  userId: string,
  data: TaskTransactionAssignment
): Promise<void> => {
  const task = await getTaskById(taskId, userId);
  if (!task) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Task not found" });
  }

  const currentTaskRecord = await getCurrentTaskRecord(taskId);
  if (!currentTaskRecord) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "No active task record found" });
  }

  // Remove specified transactions from task record
  for (const transactionId of data.transactions) {
    await db
      .update(transactions)
      .set({ taskRecordId: null })
      .where(
        and(
          eq(transactions.id, transactionId),
          eq(transactions.userId, userId),
          eq(transactions.taskRecordId, currentTaskRecord.id)
        )
      );
  }

  // Check if task record should be reset to active status
  const remainingTransactions = await db
    .select()
    .from(transactions)
    .where(and(eq(transactions.userId, userId), eq(transactions.taskRecordId, currentTaskRecord.id)));

  if (remainingTransactions.length === 0) {
    await db.update(taskRecords).set({ status: "active" }).where(eq(taskRecords.id, currentTaskRecord.id));
  } else {
    // Check for auto-completion with remaining transactions
    await checkAndCompleteTaskRecord(currentTaskRecord.id);
  }
};

// Auto-completion Logic
export const checkAndCompleteTaskRecord = async (taskRecordId: string): Promise<void> => {
  const taskRecord = await db.query.taskRecords.findFirst({
    where: eq(taskRecords.id, taskRecordId),
    with: {
      task: true,
    },
  });

  if (!taskRecord?.task.amount || taskRecord.status !== "active") {
    return;
  }

  // Calculate total amount of assigned transactions
  const result = await db
    .select({ total: sum(transactions.baseAmount) })
    .from(transactions)
    .where(eq(transactions.taskRecordId, taskRecordId));

  const totalAmount = result[0]?.total ? new Decimal(result[0].total) : new Decimal(0);
  const targetAmount = new Decimal(taskRecord.task.amount);

  if (totalAmount.gte(targetAmount)) {
    // Auto-complete the task record
    await db
      .update(taskRecords)
      .set({
        status: "completed",
        amount: totalAmount.toFixed(4),
      })
      .where(eq(taskRecords.id, taskRecordId));
  }
};
