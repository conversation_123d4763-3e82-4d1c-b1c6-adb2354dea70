import type { App<PERSON>out<PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type {
  AssignTaskTransactionsRoute,
  CreateTaskRoute,
  DeleteTaskRoute,
  GetTaskHistoryRoute,
  GetTaskRoute,
  ListTasksRoute,
  RemoveTaskTransactionsRoute,
  ReplaceTaskTransactionsRoute,
  UpdateTaskRoute,
  UpdateTaskStatusRoute,
} from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import {
  assignTransactionsToTaskRecord,
  createTask as createTaskAction,
  deleteTask as deleteTaskAction,
  ensureTaskRecordExists,
  getTaskById,
  getTaskRecordsByTaskId,
  getTasksByUserId,
  removeTransactionsFromTaskRecord,
  replaceTransactionsInTaskRecord,
  updateCurrentTaskStatus,
  updateTask as updateTaskAction,
} from "./actions";
import { mapTaskRecordResponse, mapTaskResponse } from "./mappers";

export const listTasks: AppRouteHandler<ListTasksRoute> = async (c) => {
  const user = c.get("user");
  const query = c.req.valid("query");

  const { items, total } = await getTasksByUserId(user.id, query);

  const response = {
    items: items.map(mapTaskResponse),
    meta: {
      total,
      page: query.page,
      limit: query.limit,
      count: items.length,
    },
  };

  return c.json(response, StatusCodes.OK);
};

export const createTask: AppRouteHandler<CreateTaskRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const task = await createTaskAction(user.id, data);
  return c.json(mapTaskResponse(task), StatusCodes.CREATED);
};

export const getTask: AppRouteHandler<GetTaskRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const task = await getTaskById(id, user.id);
  if (!task) {
    return c.json({ success: false, message: "Task not found" }, StatusCodes.NOT_FOUND);
  }

  // Ensure task record exists for current period (on-access creation)
  await ensureTaskRecordExists(id, user.id);

  return c.json(mapTaskResponse(task), StatusCodes.OK);
};

export const updateTask: AppRouteHandler<UpdateTaskRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const task = await updateTaskAction(id, user.id, data);
  return c.json(mapTaskResponse(task), StatusCodes.OK);
};

export const updateTaskStatus: AppRouteHandler<UpdateTaskStatusRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  await updateCurrentTaskStatus(id, user.id, data);
  return c.json({ success: true, message: "Task status updated" }, StatusCodes.OK);
};

export const deleteTask: AppRouteHandler<DeleteTaskRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const task = await getTaskById(id, user.id);
  if (!task) {
    return c.json({ success: false, message: "Task not found" }, StatusCodes.NOT_FOUND);
  }

  await deleteTaskAction(id, user.id);
  return c.body(null, StatusCodes.NO_CONTENT);
};

export const getTaskHistory: AppRouteHandler<GetTaskHistoryRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const query = c.req.valid("query");

  const { items, total } = await getTaskRecordsByTaskId(id, user.id, query);

  const response = {
    items: items.map(mapTaskRecordResponse),
    meta: {
      total,
      page: query.page,
      limit: query.limit,
      count: items.length,
    },
  };

  return c.json(response, StatusCodes.OK);
};

export const assignTaskTransactions: AppRouteHandler<AssignTaskTransactionsRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  // Ensure task record exists for current period (on-access creation)
  await ensureTaskRecordExists(id, user.id);

  await assignTransactionsToTaskRecord(id, user.id, data);
  return c.json({ success: true, message: "Transactions assigned" }, StatusCodes.OK);
};

export const replaceTaskTransactions: AppRouteHandler<ReplaceTaskTransactionsRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  // Ensure task record exists for current period (on-access creation)
  await ensureTaskRecordExists(id, user.id);

  await replaceTransactionsInTaskRecord(id, user.id, data);
  return c.json({ success: true, message: "Transactions replaced" }, StatusCodes.OK);
};

export const removeTaskTransactions: AppRouteHandler<RemoveTaskTransactionsRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  // Ensure task record exists for current period (on-access creation)
  await ensureTaskRecordExists(id, user.id);

  await removeTransactionsFromTaskRecord(id, user.id, data);
  return c.json({ success: true, message: "Transactions removed" }, StatusCodes.OK);
};
