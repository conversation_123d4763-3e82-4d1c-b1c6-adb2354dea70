import createRouter from "~/lib/router";

import * as handlers from "./handlers";
import * as routes from "./routes";

const router = createRouter()
  .openapi(routes.listTasks, handlers.listTasks)
  .openapi(routes.createTask, handlers.createTask)
  .openapi(routes.getTask, handlers.getTask)
  .openapi(routes.updateTask, handlers.updateTask)
  .openapi(routes.deleteTask, handlers.deleteTask)
  .openapi(routes.getTaskHistory, handlers.getTaskHistory)
  .openapi(routes.assignTaskTransactions, handlers.assignTaskTransactions)
  .openapi(routes.replaceTaskTransactions, handlers.replaceTaskTransactions)
  .openapi(routes.removeTaskTransactions, handlers.removeTaskTransactions);

export default router;
