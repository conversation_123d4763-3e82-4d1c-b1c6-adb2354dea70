import { createRoute, z } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import { goalCreateSchema, goalResponseSchema, goalUpdateSchema } from "./schemas";

const tags = ["Goals"];

export const listGoals = createRoute({
  tags,
  path: "/goals",
  method: "get",
  operationId: "listGoals",
  summary: "List goals",
  description: "Get all financial goals for the current user",
  security: [{ JWT: [] }],
  responses: {
    [StatusCodes.OK]: jsonContent(z.array(goalResponseSchema), "Goals retrieved"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const createGoal = createRoute({
  tags,
  path: "/goals",
  method: "post",
  operationId: "createGoal",
  summary: "Create goal",
  description: "Create a new financial goal",
  security: [{ JWT: [] }],
  request: {
    body: requestBody(goalCreateSchema, "Goal to create"),
  },
  responses: {
    [StatusCodes.CREATED]: jsonContent(goalResponseSchema, "Goal created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const getGoal = createRoute({
  tags,
  path: "/goals/{id}",
  method: "get",
  operationId: "getGoal",
  summary: "Get goal",
  description: "Get a specific financial goal by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(goalResponseSchema, "Goal retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Goal not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const updateGoal = createRoute({
  tags,
  path: "/goals/{id}",
  method: "put",
  operationId: "updateGoal",
  summary: "Update goal",
  description: "Update a financial goal",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(goalUpdateSchema, "Goal data to update"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(goalResponseSchema, "Goal updated"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.NOT_FOUND]: errorSchema("Goal not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_CONTENT]: validationErrorSchema("Validation error"),
  },
});

export const deleteGoal = createRoute({
  tags,
  path: "/goals/{id}",
  method: "delete",
  operationId: "deleteGoal",
  summary: "Delete goal",
  description: "Delete a financial goal",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(z.object({ message: z.string() }), "Goal deleted"),
    [StatusCodes.NOT_FOUND]: errorSchema("Goal not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export type ListGoalsRoute = typeof listGoals;
export type CreateGoalRoute = typeof createGoal;
export type GetGoalRoute = typeof getGoal;
export type UpdateGoalRoute = typeof updateGoal;
export type DeleteGoalRoute = typeof deleteGoal;
