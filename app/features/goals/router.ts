import createRouter from "~/lib/router";

import * as handlers from "./handlers";
import * as routes from "./routes";

const router = createRouter()
  .openapi(routes.listGoals, handlers.listGoals)
  .openapi(routes.createGoal, handlers.createGoal)
  .openapi(routes.getGoal, handlers.getGoal)
  .openapi(routes.updateGoal, handlers.updateGoal)
  .openapi(routes.deleteGoal, handlers.deleteGoal);

export default router;
