import type { Goal, GoalResponse } from "./types";

export const mapGoalResponse = (goal: Goal): GoalResponse => ({
  id: goal.id,
  name: goal.name,
  accounts: goal.accounts,
  targetAmount: goal.targetAmount,
  currentAmount: goal.currentAmount,
  description: goal.description,
  color: goal.color,
  iconUrl: goal.iconUrl,
  targetDate: goal.targetDate,
  currency: goal.currency,
  createdAt: goal.createdAt.toISOString(),
  updatedAt: goal.updatedAt.toISOString(),
});
