import type { <PERSON>pp<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type { CreateGoalRoute, DeleteGoalRoute, GetGoalRoute, ListGoalsRoute, UpdateGoalRoute } from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import {
  createGoal as createGoalAction,
  deleteGoal as deleteGoalAction,
  getGoalById,
  getGoalsByUserId,
  updateGoal as updateGoalAction,
} from "./actions";
import { mapGoalResponse } from "./mappers";

export const listGoals: AppRouteHandler<ListGoalsRoute> = async (c) => {
  const user = c.get("user");
  const goals = await getGoalsByUserId(user.id);
  return c.json(goals.map(mapGoalResponse), StatusCodes.OK);
};

export const createGoal: AppRouteHandler<CreateGoalRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const goal = await createGoalAction(user, data);
  return c.json(mapGoalResponse(goal), StatusCodes.CREATED);
};

export const getGoal: AppRouteHandler<GetGoalRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const goal = await getGoalById(id, user.id);
  if (!goal) {
    return c.json({ message: "Goal not found" }, StatusCodes.NOT_FOUND);
  }

  return c.json(mapGoalResponse(goal), StatusCodes.OK);
};

export const updateGoal: AppRouteHandler<UpdateGoalRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const goal = await updateGoalAction(id, user, data);
  return c.json(mapGoalResponse(goal), StatusCodes.OK);
};

export const deleteGoal: AppRouteHandler<DeleteGoalRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  await deleteGoalAction(id, user.id);
  return c.json({ message: "Goal deleted successfully" }, StatusCodes.OK);
};
