import { z } from "@hono/zod-openapi";

import { currency, decimal } from "~/common/schemas";

export const goalCreateSchema = z
  .object({
    name: z.string().min(1).max(100).openapi({ example: "New Car" }),
    accounts: z
      .array(z.string().uuid())
      .min(1)
      .openapi({ example: ["123e4567-e89b-12d3-a456-************"] }),
    targetAmount: decimal().optional(),
    description: z.string().optional().openapi({ example: "Saving for a new Tesla Model 3" }),
    color: z.string().optional().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().url().optional().openapi({ example: "https://example.com/car-icon.png" }),
    targetDate: z.string().date().optional().openapi({ example: "2025-12-31" }),
  })
  .openapi("GoalCreateRequest");

export const goalUpdateSchema = z
  .object({
    name: z.string().min(1).max(100).optional().openapi({ example: "New Car" }),
    accounts: z
      .array(z.string().uuid())
      .min(1)
      .optional()
      .openapi({ example: ["123e4567-e89b-12d3-a456-************"] }),
    targetAmount: decimal().optional(),
    description: z.string().optional().openapi({ example: "Saving for a new Tesla Model 3" }),
    color: z.string().optional().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().url().optional().openapi({ example: "https://example.com/car-icon.png" }),
    targetDate: z.string().date().optional().openapi({ example: "2025-12-31" }),
  })
  .openapi("GoalUpdateRequest");

export const goalResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().openapi({ example: "New Car" }),
    accounts: z.array(z.string().uuid()).openapi({ example: ["123e4567-e89b-12d3-a456-************"] }),
    targetAmount: decimal().nullable(),
    currentAmount: decimal().openapi({ example: "5000.0000" }),
    description: z.string().nullable().openapi({ example: "Saving for a new Tesla Model 3" }),
    color: z.string().nullable().openapi({ example: "#3B82F6" }),
    iconUrl: z.string().nullable().openapi({ example: "https://example.com/car-icon.png" }),
    targetDate: z.string().date().nullable().openapi({ example: "2025-12-31" }),
    currency: currency("Goal currency"),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00.000Z" }),
  })
  .openapi("Goal");
