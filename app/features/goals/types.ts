import type { goalSelectSchema } from "~/db/schemas";
import type { goalCreateSchema, goalResponseSchema, goalUpdateSchema } from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

export type CreateGoalData = z.infer<typeof goalCreateSchema>;
export type UpdateGoalData = z.infer<typeof goalUpdateSchema>;
export type Goal = z4.infer<typeof goalSelectSchema>;
export type GoalResponse = z.infer<typeof goalResponseSchema>;
