import type { AccountDeletedEvent, AccountUpdatedEvent, Events } from "~/events/types";
import type { AppBindings } from "~/types";

import { defineHandler } from "@hono/event-emitter";

import { removeAccountFromGoals, updateGoalsForAccount } from "./actions";

export const accountUpdated = defineHandler<Events, typeof AccountUpdatedEvent, AppBindings>(async (_c, payload) => {
  await updateGoalsForAccount(payload.account.id, payload.account.userId);
});

export const accountDeleted = defineHandler<Events, typeof AccountDeletedEvent, AppBindings>(async (_c, payload) => {
  await removeAccountFromGoals(payload.account.id, payload.account.userId);
});
