import type { User } from "~/features/users/types";
import type { CreateGoalData, Goal, UpdateGoalData } from "./types";

import Decimal from "decimal.js";
import { and, eq, inArray } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { accounts, goals } from "~/db/schemas";
import { StatusCodes } from "~/lib/status-codes";

export const createGoal = async (user: User, data: CreateGoalData): Promise<Goal> => {
  // Validate that all accounts exist, are active, and belong to the user
  const userAccounts = await db
    .select({ id: accounts.id, currency: accounts.currency, currentBalance: accounts.currentBalance })
    .from(accounts)
    .where(and(eq(accounts.userId, user.id), inArray(accounts.id, data.accounts), eq(accounts.isActive, true)));

  if (userAccounts.length !== data.accounts.length) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, {
      message: "One or more accounts not found or inactive",
    });
  }

  // Validate that all accounts have the same currency
  const currencies = [...new Set(userAccounts.map((acc) => acc.currency))];
  if (currencies.length > 1) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, {
      message: "All linked accounts must have the same currency",
    });
  }

  const currency = currencies[0];

  // Calculate current amount by summing account balances
  const currentAmount = userAccounts
    .reduce((sum, acc) => sum.plus(new Decimal(acc.currentBalance)), new Decimal(0))
    .toFixed(4);

  const [goal] = await db
    .insert(goals)
    .values({
      userId: user.id,
      ...data,
      currency,
      currentAmount,
    })
    .returning();

  if (!goal) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create goal" });
  }

  return goal;
};

export const getGoalsByUserId = async (userId: string): Promise<Goal[]> => {
  return await db.select().from(goals).where(eq(goals.userId, userId));
};

export const getGoalById = async (id: string, userId: string): Promise<Goal | undefined> => {
  const [goal] = await db
    .select()
    .from(goals)
    .where(and(eq(goals.id, id), eq(goals.userId, userId)));

  return goal;
};

export const updateGoal = async (id: string, user: User, data: UpdateGoalData): Promise<Goal> => {
  const existingGoal = await getGoalById(id, user.id);
  if (!existingGoal) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Goal not found" });
  }

  let updateData = { ...data };

  // If accounts are being updated, validate and recalculate current amount
  if (data.accounts) {
    // Validate that all accounts exist, are active, and belong to the user
    const userAccounts = await db
      .select({ id: accounts.id, currency: accounts.currency, currentBalance: accounts.currentBalance })
      .from(accounts)
      .where(and(eq(accounts.userId, user.id), inArray(accounts.id, data.accounts), eq(accounts.isActive, true)));

    if (userAccounts.length !== data.accounts.length) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "One or more accounts not found or inactive",
      });
    }

    // Validate that all accounts have the same currency
    const currencies = [...new Set(userAccounts.map((acc) => acc.currency))];
    if (currencies.length > 1) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "All linked accounts must have the same currency",
      });
    }

    const currency = currencies[0];

    // Calculate current amount by summing account balances
    const currentAmount = userAccounts
      .reduce((sum, acc) => sum.plus(new Decimal(acc.currentBalance)), new Decimal(0))
      .toFixed(4);

    updateData = {
      ...updateData,
      currency,
      currentAmount,
    };
  }

  const [goal] = await db
    .update(goals)
    .set(updateData)
    .where(and(eq(goals.id, id), eq(goals.userId, user.id)))
    .returning();

  if (!goal) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Goal not found" });
  }

  return goal;
};

export const deleteGoal = async (id: string, userId: string): Promise<void> => {
  const result = await db
    .delete(goals)
    .where(and(eq(goals.id, id), eq(goals.userId, userId)))
    .returning({ id: goals.id });

  if (result.length === 0) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Goal not found" });
  }
};

export const recalculateGoalCurrentAmount = async (goalId: string, userId: string): Promise<Goal> => {
  const goal = await getGoalById(goalId, userId);
  if (!goal) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Goal not found" });
  }

  // Get current balances of all linked accounts
  const userAccounts = await db
    .select({ currentBalance: accounts.currentBalance })
    .from(accounts)
    .where(and(eq(accounts.userId, userId), inArray(accounts.id, goal.accounts), eq(accounts.isActive, true)));

  // Calculate current amount by summing account balances
  const currentAmount = userAccounts
    .reduce((sum, acc) => sum.plus(new Decimal(acc.currentBalance)), new Decimal(0))
    .toFixed(4);

  const [updatedGoal] = await db
    .update(goals)
    .set({ currentAmount })
    .where(and(eq(goals.id, goalId), eq(goals.userId, userId)))
    .returning();

  if (!updatedGoal) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Goal not found" });
  }

  return updatedGoal;
};

export const removeAccountFromGoals = async (accountId: string, userId: string): Promise<void> => {
  // Find all goals that contain this account
  const goalsWithAccount = await db.select().from(goals).where(eq(goals.userId, userId));

  for (const goal of goalsWithAccount) {
    if (goal.accounts.includes(accountId)) {
      const updatedAccounts = goal.accounts.filter((id) => id !== accountId);

      // If this was the last account, delete the goal
      if (updatedAccounts.length === 0) {
        await db.delete(goals).where(eq(goals.id, goal.id));
      } else {
        // Otherwise, update the goal with remaining accounts and recalculate current amount
        const userAccounts = await db
          .select({ currentBalance: accounts.currentBalance })
          .from(accounts)
          .where(and(eq(accounts.userId, userId), inArray(accounts.id, updatedAccounts), eq(accounts.isActive, true)));

        const currentAmount = userAccounts
          .reduce((sum, acc) => sum.plus(new Decimal(acc.currentBalance)), new Decimal(0))
          .toFixed(4);

        await db
          .update(goals)
          .set({
            accounts: updatedAccounts,
            currentAmount,
          })
          .where(eq(goals.id, goal.id));
      }
    }
  }
};

export const updateGoalsForAccount = async (accountId: string, userId: string): Promise<void> => {
  // Find all goals that contain this account
  const goalsWithAccount = await db.select().from(goals).where(eq(goals.userId, userId));

  for (const goal of goalsWithAccount) {
    if (goal.accounts.includes(accountId)) {
      await recalculateGoalCurrentAmount(goal.id, userId);
    }
  }
};
