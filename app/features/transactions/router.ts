import createRouter from "~/lib/router";

import * as handlers from "./handlers";
import * as routes from "./routes";

const router = createRouter()
  .openapi(routes.listTransactions, handlers.listTransactions)
  .openapi(routes.createTransaction, handlers.createTransaction)
  .openapi(routes.getTransaction, handlers.getTransaction)
  .openapi(routes.updateTransaction, handlers.updateTransaction)
  .openapi(routes.deleteTransaction, handlers.deleteTransaction);

export default router;
