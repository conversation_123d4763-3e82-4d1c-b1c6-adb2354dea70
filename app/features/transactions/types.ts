import type { transactionSelectSchema } from "~/db/schemas";
import type { AccountWithGroup } from "~/features/accounts/types";
import type { Category } from "~/features/categories/types";
import type {
  transactionCreateSchema,
  transactionListQuerySchema,
  transactionListResponseSchema,
  transactionResponseSchema,
  transactionUpdateSchema,
} from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

// Transaction Types
export type CreateTransactionData = z.infer<typeof transactionCreateSchema>;
export type UpdateTransactionData = z.infer<typeof transactionUpdateSchema>;
export type Transaction = z4.infer<typeof transactionSelectSchema>;
export type TransactionListQuery = z.infer<typeof transactionListQuerySchema>;
export type TransactionListResponse = z.infer<typeof transactionListResponseSchema>;

export type TransactionWithRelations = Transaction & {
  account: AccountWithGroup;
  accountTo: AccountWithGroup | null;
  category: Category | null;
};

export type TransactionResponse = z.infer<typeof transactionResponseSchema>;
