import type { User } from "~/features/users/types";
import type {
  CreateTransactionData,
  Transaction,
  TransactionListQuery,
  TransactionWithRelations,
  UpdateTransactionData,
} from "./types";

import { and, desc, eq } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { transactions } from "~/db/schemas";
import { getAccountById } from "~/features/accounts/actions";
import { convertAmount } from "~/features/currencies/actions";
import { StatusCodes } from "~/lib/status-codes";

export const getTransactionsByUserId = async (
  userId: string,
  query: TransactionListQuery
): Promise<{ items: TransactionWithRelations[]; total: number }> => {
  const { page, limit } = query;
  const offset = (page - 1) * limit;

  // Get total count
  const total = await db.$count(transactions, eq(transactions.userId, userId));

  // Get paginated transactions
  const items = await db.query.transactions.findMany({
    where: eq(transactions.userId, userId),
    orderBy: [desc(transactions.transactionDate), desc(transactions.createdAt)],
    limit,
    offset,
    with: {
      account: {
        with: {
          group: true,
        },
      },
      accountTo: {
        with: {
          group: true,
        },
      },
      category: true,
    },
  });

  return { items, total };
};

export const getTransactionById = async (id: string, userId: string) => {
  const transaction = await db.query.transactions.findFirst({
    where: and(eq(transactions.id, id), eq(transactions.userId, userId)),
  });

  return transaction ?? null;
};

export const getTransactionWithRelationsById = async (id: string, userId: string) => {
  const transaction = await db.query.transactions.findFirst({
    where: and(eq(transactions.id, id), eq(transactions.userId, userId)),
    with: {
      account: {
        with: {
          group: true,
        },
      },
      accountTo: {
        with: {
          group: true,
        },
      },
      category: true,
    },
  });

  return transaction ?? null;
};

export const createTransaction = async (user: User, data: CreateTransactionData): Promise<Transaction> => {
  // Validate that the account belongs to the user
  const account = await getAccountById(data.accountId, user.id);

  if (!account) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account not found" });
  }

  // For transfer transactions, validate the destination account
  let accountTo = null;
  if (data.type === "transfer") {
    if (!data.accountToId || !data.amountTo) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "For transfer transactions, accountToId and amountTo are required",
      });
    }

    const foundAccountTo = await getAccountById(data.accountToId, user.id);

    if (!foundAccountTo) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Destination account not found" });
    }

    accountTo = foundAccountTo;
  } else {
    // For non-transfer transactions, ensure accountToId and amountTo are not provided
    if (data.accountToId || data.amountTo) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "For non-transfer transactions, accountToId and amountTo should not be provided",
      });
    }
  }

  // Convert amounts to base currency
  const baseAmount = await convertAmount(data.amount, account.currency, user.baseCurrency, data.transactionDate);

  let baseAmountTo = null;
  if (data.type === "transfer" && accountTo && data.amountTo) {
    baseAmountTo = await convertAmount(data.amountTo, accountTo.currency, user.baseCurrency, data.transactionDate);
  }

  const [transaction] = await db
    .insert(transactions)
    .values({
      userId: user.id,
      transactionDate: data.transactionDate,
      categoryId: data.categoryId,
      type: data.type,
      description: data.description,
      accountId: data.accountId,
      amount: data.amount,
      accountToId: data.accountToId,
      amountTo: data.amountTo,
      baseAmount,
      baseAmountTo,
    })
    .returning();

  if (!transaction) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create transaction" });
  }

  return transaction;
};

export const updateTransaction = async (id: string, user: User, data: UpdateTransactionData): Promise<Transaction> => {
  // Check if transaction exists and belongs to user
  const existingTransaction = await getTransactionById(id, user.id);
  if (!existingTransaction) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Transaction not found" });
  }

  const existingAccount = await getAccountById(existingTransaction.accountId, user.id);
  if (!existingAccount) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Broken transaction" });
  }

  // Validate account if provided
  let newAccount = null;
  if (data.accountId) {
    const foundAccount = await getAccountById(data.accountId, user.id);

    if (!foundAccount) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Account not found" });
    }

    newAccount = foundAccount;
  }

  // For transfer transactions, validate the destination account
  let accountTo = null;
  if (data.type === "transfer" || (existingTransaction.type === "transfer" && !data.type)) {
    if (!data.accountToId && !existingTransaction.accountToId) {
      throw new HTTPException(StatusCodes.BAD_REQUEST, {
        message: "For transfer transactions, accountToId is required",
      });
    }

    const accountToIdToCheck = data.accountToId ?? existingTransaction.accountToId;
    if (accountToIdToCheck) {
      const foundAccountTo = await getAccountById(accountToIdToCheck, user.id);

      if (!foundAccountTo) {
        throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Destination account not found" });
      }

      accountTo = foundAccountTo;
    }
  }

  // Prepare update data
  const updateData: Partial<Transaction> = {};

  // Copy provided fields
  if (data.transactionDate !== undefined) updateData.transactionDate = data.transactionDate;
  if (data.categoryId !== undefined) updateData.categoryId = data.categoryId;
  if (data.type !== undefined) updateData.type = data.type;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.accountId !== undefined) updateData.accountId = data.accountId;
  if (data.amount !== undefined) updateData.amount = data.amount;
  if (data.accountToId !== undefined) updateData.accountToId = data.accountToId;
  if (data.amountTo !== undefined) updateData.amountTo = data.amountTo;

  // Recalculate base amounts if necessary
  const finalAccount = newAccount ?? existingAccount;
  const finalTransactionDate = data.transactionDate ?? existingTransaction.transactionDate;
  const finalAmount = data.amount ?? existingTransaction.amount;

  if (data.amount !== undefined || data.accountId !== undefined || data.transactionDate !== undefined) {
    updateData.baseAmount = await convertAmount(
      finalAmount,
      finalAccount.currency,
      user.baseCurrency,
      finalTransactionDate
    );
  }

  const finalType = data.type ?? existingTransaction.type;
  if (finalType === "transfer") {
    const finalAccountTo =
      accountTo ?? (await getAccountById(data.accountToId ?? existingTransaction.accountToId!, user.id));
    const finalAmountTo = data.amountTo ?? existingTransaction.amountTo;

    if (
      finalAmountTo &&
      (data.amountTo !== undefined || data.accountToId !== undefined || data.transactionDate !== undefined)
    ) {
      updateData.baseAmountTo = await convertAmount(
        finalAmountTo,
        finalAccountTo!.currency,
        user.baseCurrency,
        finalTransactionDate
      );
    }
  } else {
    // For non-transfer transactions, clear transfer-related fields
    updateData.accountToId = null;
    updateData.amountTo = null;
    updateData.baseAmountTo = null;
  }

  const [updatedTransaction] = await db
    .update(transactions)
    .set(updateData)
    .where(and(eq(transactions.id, id), eq(transactions.userId, user.id)))
    .returning();

  if (!updatedTransaction) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to update transaction" });
  }

  return updatedTransaction;
};

export const deleteTransaction = async (id: string, userId: string) => {
  await db.delete(transactions).where(and(eq(transactions.id, id), eq(transactions.userId, userId)));
};
