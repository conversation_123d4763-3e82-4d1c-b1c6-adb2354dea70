import { createRoute, z } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import {
  transactionCreateSchema,
  transactionListQuerySchema,
  transactionListResponseSchema,
  transactionResponseSchema,
  transactionUpdateSchema,
} from "./schemas";

const tags = ["Transactions"];

export const listTransactions = createRoute({
  tags,
  path: "/transactions",
  method: "get",
  operationId: "listTransactions",
  summary: "List transactions",
  description: "Get paginated list of transactions for the authenticated user",
  security: [{ JWT: [] }],
  request: {
    query: transactionListQuerySchema,
  },
  responses: {
    [StatusCodes.OK]: jsonContent(transactionListResponseSchema, "Paginated list of transactions"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const createTransaction = createRoute({
  tags,
  path: "/transactions",
  method: "post",
  operationId: "createTransaction",
  summary: "Create transaction",
  description: "Create a new transaction",
  security: [{ JWT: [] }],
  request: {
    body: requestBody(transactionCreateSchema, "Transaction data"),
  },
  responses: {
    [StatusCodes.CREATED]: jsonContent(transactionResponseSchema, "Transaction created successfully"),
    [StatusCodes.BAD_REQUEST]: validationErrorSchema("Validation error"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const getTransaction = createRoute({
  tags,
  path: "/transactions/{id}",
  method: "get",
  operationId: "getTransaction",
  summary: "Get transaction",
  description: "Get a specific transaction by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(transactionResponseSchema, "Transaction details"),
    [StatusCodes.NOT_FOUND]: errorSchema("Transaction not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const updateTransaction = createRoute({
  tags,
  path: "/transactions/{id}",
  method: "put",
  operationId: "updateTransaction",
  summary: "Update transaction",
  description: "Update an existing transaction",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(transactionUpdateSchema, "Updated transaction data"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(transactionResponseSchema, "Transaction updated successfully"),
    [StatusCodes.BAD_REQUEST]: validationErrorSchema("Validation error"),
    [StatusCodes.NOT_FOUND]: errorSchema("Transaction not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export const deleteTransaction = createRoute({
  tags,
  path: "/transactions/{id}",
  method: "delete",
  operationId: "deleteTransaction",
  summary: "Delete transaction",
  description: "Delete a transaction",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.NO_CONTENT]: { description: "Transaction deleted successfully" },
    [StatusCodes.NOT_FOUND]: errorSchema("Transaction not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
  },
});

export type ListTransactionsRoute = typeof listTransactions;
export type CreateTransactionRoute = typeof createTransaction;
export type GetTransactionRoute = typeof getTransaction;
export type UpdateTransactionRoute = typeof updateTransaction;
export type DeleteTransactionRoute = typeof deleteTransaction;
