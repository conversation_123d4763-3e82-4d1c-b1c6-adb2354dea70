import createRouter from "~/lib/router";

import * as handlers from "./handlers";
import * as routes from "./routes";

const router = createRouter()
  .openapi(routes.listBudgets, handlers.listBudgets)
  .openapi(routes.createBudget, handlers.createBudget)
  .openapi(routes.getBudget, handlers.getBudget)
  .openapi(routes.updateBudget, handlers.updateBudget)
  .openapi(routes.deleteBudget, handlers.deleteBudget)
  .openapi(routes.getBudgetHistory, handlers.getBudgetHistory);

export default router;
