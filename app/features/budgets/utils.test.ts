import { describe, expect, it } from "vitest";

import { getPeriodDates } from "./utils";

describe("Budget Utils", () => {
  describe("getPeriodDates", () => {
    it("should calculate correct period dates for month", () => {
      const testDate = new Date("2024-01-15");
      const { startDate, endDate } = getPeriodDates("month", testDate);

      expect(startDate).toBe("2024-01-01");
      expect(endDate).toBe("2024-01-31");
    });

    it("should calculate correct period dates for week", () => {
      const testDate = new Date("2024-01-15"); // Monday
      const { startDate, endDate } = getPeriodDates("week", testDate);

      // Week should start on Monday
      expect(new Date(startDate).getDay()).toBe(1);
      expect(new Date(endDate).getDay()).toBe(0); // Sunday
    });

    it("should calculate correct period dates for quarter", () => {
      const testDate = new Date("2024-02-15");
      const { startDate, endDate } = getPeriodDates("quarter", testDate);

      expect(startDate).toBe("2024-01-01");
      expect(endDate).toBe("2024-03-31");
    });

    it("should calculate correct period dates for year", () => {
      const testDate = new Date("2024-06-15");
      const { startDate, endDate } = getPeriodDates("year", testDate);

      expect(startDate).toBe("2024-01-01");
      expect(endDate).toBe("2024-12-31");
    });
  });
});
