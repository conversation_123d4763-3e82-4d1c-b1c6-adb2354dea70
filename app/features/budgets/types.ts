import type { budgetRecordSelectSchema, budgetSelectSchema } from "~/db/schemas";
import type {
  budgetCreateSchema,
  budgetHistoryQuerySchema,
  budgetHistoryResponseSchema,
  budgetListQuerySchema,
  budgetListResponseSchema,
  budgetRecordResponseSchema,
  budgetResponseSchema,
  budgetUpdateSchema,
} from "./schemas";

import { z } from "@hono/zod-openapi";
import { z as z4 } from "zod/v4";

// Budget Types
export type CreateBudgetData = z.infer<typeof budgetCreateSchema>;
export type UpdateBudgetData = z.infer<typeof budgetUpdateSchema>;
export type Budget = z4.infer<typeof budgetSelectSchema>;
export type BudgetResponse = z.infer<typeof budgetResponseSchema>;
export type BudgetListQuery = z.infer<typeof budgetListQuerySchema>;
export type BudgetListResponse = z.infer<typeof budgetListResponseSchema>;

// Budget Record Types
export type BudgetRecord = z4.infer<typeof budgetRecordSelectSchema>;
export type BudgetRecordResponse = z.infer<typeof budgetRecordResponseSchema>;
export type BudgetHistoryQuery = z.infer<typeof budgetHistoryQuerySchema>;
export type BudgetHistoryResponse = z.infer<typeof budgetHistoryResponseSchema>;

// Extended Types
export type BudgetWithCurrentRecord = Budget & {
  currentRecord?: BudgetRecord;
};

// Period Types
export type BudgetPeriod = "week" | "month" | "quarter" | "year";
export type BudgetType = "fixed" | "percentage";

// Period Calculation Result
export type PeriodDates = {
  startDate: string;
  endDate: string;
};
