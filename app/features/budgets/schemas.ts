import { z } from "@hono/zod-openapi";

import { budgetPeriod, budgetType, decimal, paginationMeta, paginationQuery } from "~/common/schemas";

// Budget Schemas
export const budgetCreateSchema = z
  .object({
    name: z.string().min(1).max(100).openapi({ example: "Monthly Groceries" }),
    description: z.string().nullish().openapi({ example: "Budget for grocery shopping" }),
    period: budgetPeriod("Budget period"),
    type: budgetType("Budget type"),
    value: decimal(),
    accounts: z
      .array(z.string().uuid())
      .nullish()
      .openapi({
        example: ["123e4567-e89b-12d3-a456-************"],
        description: "Optional list of account IDs to restrict budget to",
      }),
  })
  .openapi("BudgetCreateRequest");

export const budgetUpdateSchema = z
  .object({
    name: z.string().min(1).max(100).optional().openapi({ example: "Monthly Groceries" }),
    description: z.string().nullish().openapi({ example: "Budget for grocery shopping" }),
    type: budgetType("Budget type").optional(),
    value: decimal().optional(),
    accounts: z
      .array(z.string().uuid())
      .nullish()
      .openapi({
        example: ["123e4567-e89b-12d3-a456-************"],
        description: "Optional list of account IDs to restrict budget to",
      }),
    isArchived: z.boolean().optional().openapi({ example: false }),
  })
  .openapi("BudgetUpdateRequest");

// Budget Record Schema
export const budgetRecordResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    budgetId: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    startDate: z.string().date().openapi({ example: "2024-01-01" }),
    endDate: z.string().date().openapi({ example: "2024-01-31" }),
    plannedAmount: decimal(),
    usedAmount: decimal(),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-15T10:30:00Z" }),
  })
  .openapi("BudgetRecord", { description: "Budget record for a specific or current period" });

// Budget Response Schema
export const budgetResponseSchema = z
  .object({
    id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    name: z.string().openapi({ example: "Monthly Groceries" }),
    description: z.string().nullable().openapi({ example: "Budget for grocery shopping" }),
    period: budgetPeriod("Budget period"),
    type: budgetType("Budget type"),
    value: decimal(),
    accounts: z
      .array(z.string().uuid())
      .nullable()
      .openapi({
        example: ["123e4567-e89b-12d3-a456-************"],
        description: "List of account IDs this budget is restricted to",
      }),
    isArchived: z.boolean().openapi({ example: false }),
    currentRecord: budgetRecordResponseSchema.optional(),
    createdAt: z.string().datetime().openapi({ example: "2024-01-01T00:00:00Z" }),
    updatedAt: z.string().datetime().openapi({ example: "2024-01-15T10:30:00Z" }),
  })
  .openapi("Budget");

// List Query Schema
export const budgetListQuerySchema = z
  .object({
    includeArchived: z.coerce.boolean().default(false).openapi({ example: false }),
  })
  .openapi("BudgetListQuery");

// Budget History Query Schema
export const budgetHistoryQuerySchema = paginationQuery();

// List Response Schema
export const budgetListResponseSchema = z.array(budgetResponseSchema).openapi("BudgetListResponse");

// Budget History Response Schema
export const budgetHistoryResponseSchema = z
  .object({
    items: z.array(budgetRecordResponseSchema),
    meta: paginationMeta(),
  })
  .openapi("BudgetHistoryResponse");
