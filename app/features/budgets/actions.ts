import type { User } from "~/features/users/types";
import type {
  Budget,
  BudgetHistoryQuery,
  BudgetListQuery,
  BudgetRecord,
  BudgetWithCurrentRecord,
  CreateBudgetData,
  UpdateBudgetData,
} from "./types";

import Decimal from "decimal.js";
import { and, between, desc, eq, inArray, sum } from "drizzle-orm";
import { HTTPException } from "hono/http-exception";

import db from "~/db";
import { budgetRecords, budgets, transactions } from "~/db/schemas";
import { StatusCodes } from "~/lib/status-codes";

import { getPeriodDates } from "./utils";

// Budget CRUD Operations
export const createBudget = async (user: User, data: CreateBudgetData): Promise<BudgetWithCurrentRecord> => {
  const budget: BudgetWithCurrentRecord | undefined = await db
    .insert(budgets)
    .values({
      userId: user.id,
      ...data,
      accounts: data.accounts ?? null,
    })
    .returning()
    .then((res) => res[0]);

  if (!budget) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create budget" });
  }

  budget.currentRecord = await getOrCreateBudgetRecord(budget);

  return budget;
};

export const getBudgetsByUserId = async (
  userId: string,
  query: BudgetListQuery
): Promise<BudgetWithCurrentRecord[]> => {
  const { includeArchived } = query;

  // Build where condition
  const whereCondition = includeArchived
    ? eq(budgets.userId, userId)
    : and(eq(budgets.userId, userId), eq(budgets.isArchived, false));

  // Get all budgets
  const items = await db.query.budgets.findMany({
    where: whereCondition,
    orderBy: [desc(budgets.createdAt)],
  });

  // Enhance each budget with current record
  const enhancedItems = await Promise.all(
    items.map(async (budget) => {
      const currentRecord = await getOrCreateBudgetRecord(budget);
      return { ...budget, currentRecord };
    })
  );

  return enhancedItems;
};

export const getBudgetById = async (id: string, userId: string): Promise<BudgetWithCurrentRecord | null> => {
  const budget = await db.query.budgets.findFirst({
    where: and(eq(budgets.id, id), eq(budgets.userId, userId)),
  });

  if (!budget) {
    return null;
  }

  const currentRecord = await getOrCreateBudgetRecord(budget);
  return { ...budget, currentRecord };
};

export const updateBudget = async (
  id: string,
  user: User,
  data: UpdateBudgetData
): Promise<BudgetWithCurrentRecord> => {
  const updatedBudget: BudgetWithCurrentRecord | undefined = await db
    .update(budgets)
    .set({
      ...data,
      accounts: data.accounts ?? null,
    })
    .where(and(eq(budgets.id, id), eq(budgets.userId, user.id)))
    .returning()
    .then((res) => res[0]);

  if (!updatedBudget) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Budget not found" });
  }

  updatedBudget.currentRecord = await updateBudgetRecord(updatedBudget);

  return updatedBudget;
};

export const deleteBudget = async (id: string, userId: string): Promise<void> => {
  await db.delete(budgets).where(and(eq(budgets.id, id), eq(budgets.userId, userId)));
};

// Budget Record Operations
export const getOrCreateBudgetRecord = async (budget: Budget, refDate?: string): Promise<BudgetRecord> => {
  // TODO: add caching

  const { startDate, endDate } = getPeriodDates(budget.period, refDate);

  // Check if record exists for current period
  let record = await db.query.budgetRecords.findFirst({
    where: and(
      eq(budgetRecords.budgetId, budget.id),
      eq(budgetRecords.startDate, startDate),
      eq(budgetRecords.endDate, endDate)
    ),
  });

  // If no record exists, create one
  record ??= await createBudgetRecord(budget, startDate, endDate);

  return record;
};

export const createBudgetRecord = async (budget: Budget, startDate: string, endDate: string): Promise<BudgetRecord> => {
  const budgetId = budget.id;

  // Calculate planned amount
  const plannedAmount = await calculatePlannedAmount(budget, startDate, endDate);

  // Calculate used amount from transactions
  const usedAmount = await calculateUsedAmount(budget, startDate, endDate);

  const [record] = await db
    .insert(budgetRecords)
    .values({ budgetId, startDate, endDate, plannedAmount, usedAmount })
    .returning();

  if (!record) {
    throw new HTTPException(StatusCodes.BAD_REQUEST, { message: "Failed to create budget record" });
  }

  return record;
};

export const getBudgetHistory = async (
  budgetId: string,
  userId: string,
  query: BudgetHistoryQuery
): Promise<{ items: BudgetRecord[]; total: number }> => {
  const { page, limit } = query;
  const offset = (page - 1) * limit;

  // Verify budget belongs to user
  const budget = await db.query.budgets.findFirst({
    where: and(eq(budgets.id, budgetId), eq(budgets.userId, userId)),
  });

  if (!budget) {
    throw new HTTPException(StatusCodes.NOT_FOUND, { message: "Budget not found" });
  }

  // Get total count
  const total = await db.$count(budgetRecords, eq(budgetRecords.budgetId, budgetId));

  // Get paginated records
  const items = await db.query.budgetRecords.findMany({
    where: eq(budgetRecords.budgetId, budgetId),
    orderBy: [desc(budgetRecords.startDate)],
    limit,
    offset,
  });

  return { items, total };
};

// Helper Functions
const calculatePlannedAmount = async (budget: Budget, startDate: string, endDate: string): Promise<string> => {
  if (budget.type === "fixed") {
    return budget.value;
  }

  // Calculate income for the current budget period
  const income = await calculateTransactionAmountForPeriod(budget, "income", startDate, endDate);

  const percentage = new Decimal(budget.value);
  const incomeDecimal = new Decimal(income);

  return percentage.div(100).mul(incomeDecimal).toFixed(4);
};

const calculateUsedAmount = async (budget: Budget, startDate: string, endDate: string): Promise<string> => {
  return calculateTransactionAmountForPeriod(budget, "expense", startDate, endDate);
};

const calculateTransactionAmountForPeriod = async (
  budget: Budget,
  transactionType: "income" | "expense",
  startDate: string,
  endDate: string
): Promise<string> => {
  // Build query conditions
  let whereConditions = and(
    eq(transactions.userId, budget.userId),
    eq(transactions.type, transactionType),
    between(transactions.transactionDate, startDate, endDate)
  );

  // Add account restrictions if specified
  if (budget.accounts && budget.accounts.length > 0) {
    whereConditions = and(whereConditions, inArray(transactions.accountId, budget.accounts));
  }

  // Calculate sum in base currency
  const result = await db
    .select({
      total: sum(transactions.baseAmount),
    })
    .from(transactions)
    .where(whereConditions);

  return result[0]?.total ?? "0.0000";
};

// Update budget record amounts
export const updateBudgetRecord = async (budget: Budget, refDate?: string) => {
  const budgetRecord = await getOrCreateBudgetRecord(budget, refDate);

  const plannedAmount = await calculatePlannedAmount(budget, budgetRecord.startDate, budgetRecord.endDate);
  const usedAmount = await calculateUsedAmount(budget, budgetRecord.startDate, budgetRecord.endDate);

  await db.update(budgetRecords).set({ plannedAmount, usedAmount }).where(eq(budgetRecords.id, budgetRecord.id));

  return budgetRecord;
};
