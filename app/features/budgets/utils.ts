import type { BudgetPeriod, PeriodDates } from "./types";

import {
  endOfMonth,
  endOfQuarter,
  endOfWeek,
  endOfYear,
  formatISO,
  startOfMonth,
  startOfQuarter,
  startOfWeek,
  startOfYear,
} from "date-fns";

/**
 * Calculate the start and end dates for a budget period containing the given date
 */
export const getPeriodDates = (period: BudgetPeriod, date: Date | string = new Date()): PeriodDates => {
  switch (period) {
    case "week":
      return {
        startDate: formatISO(startOfWeek(date, { weekStartsOn: 1 }), { representation: "date" }), // Monday start
        endDate: formatISO(endOfWeek(date, { weekStartsOn: 1 }), { representation: "date" }),
      };
    case "month":
      return {
        startDate: formatISO(startOfMonth(date), { representation: "date" }),
        endDate: formatISO(endOfMonth(date), { representation: "date" }),
      };
    case "quarter":
      return {
        startDate: formatISO(startOfQuarter(date), { representation: "date" }),
        endDate: formatISO(endOfQuarter(date), { representation: "date" }),
      };
    case "year":
      return {
        startDate: formatISO(startOfYear(date), { representation: "date" }),
        endDate: formatISO(endOfYear(date), { representation: "date" }),
      };
    default:
      throw new Error(`Unsupported budget period: ${period as string}`);
  }
};

/**
 * Check if a date falls within a budget period
 */
export const isDateInPeriod = (date: Date, startDate: Date, endDate: Date): boolean => {
  return date >= startDate && date <= endDate;
};

/**
 * Format period dates for display
 */
export const formatPeriod = (period: BudgetPeriod, date: Date = new Date()): string => {
  const { startDate, endDate } = getPeriodDates(period, date);
  switch (period) {
    case "week":
      return `Week of ${new Date(startDate).toLocaleDateString()}`;
    case "month":
      return new Date(startDate).toLocaleDateString("en-US", { year: "numeric", month: "long" });
    case "quarter":
      return `Q${Math.floor(new Date(startDate).getMonth() / 3) + 1} ${new Date(startDate).getFullYear()}`;
    case "year":
      return new Date(startDate).getFullYear().toString();
    default:
      return `${new Date(startDate).toLocaleDateString()} - ${new Date(endDate).toLocaleDateString()}`;
  }
};
