import type { Events, TransactionCreatedEvent, TransactionDeletedEvent, TransactionUpdatedEvent } from "~/events/types";
import type { Transaction } from "~/features/transactions/types";
import type { AppBindings } from "~/types";

import { defineHandler } from "@hono/event-emitter";
import { and, arrayContained, eq, isNull, or, sql } from "drizzle-orm";

import db from "~/db";
import { budgets } from "~/db/schemas";

import { updateBudgetRecord } from "./actions";

export const transactionCreated = defineHandler<Events, typeof TransactionCreatedEvent, AppBindings>(
  async (_c, payload) => {
    await handleUpdateBudgets(payload.transaction);
  }
);

export const transactionUpdated = defineHandler<Events, typeof TransactionUpdatedEvent, AppBindings>(
  async (_c, payload) => {
    await handleUpdateBudgets(payload.transaction);
  }
);

export const transactionDeleted = defineHandler<Events, typeof TransactionDeletedEvent, AppBindings>(
  async (_c, payload) => {
    await handleUpdateBudgets(payload.transaction);
  }
);

const handleUpdateBudgets = async (transaction: Transaction) => {
  // Find all budgets for this user that might be affected
  const userBudgets = await db.query.budgets.findMany({
    where: and(
      eq(budgets.userId, transaction.userId),
      eq(budgets.isArchived, false),
      or(
        // Budget has no account restrictions or it's empty array
        or(isNull(budgets.accounts), eq(sql`array_length(${budgets.accounts}, 1)`, 0)),
        // Transaction account is in the budget's account list
        arrayContained(sql`${transaction.accountId}`, budgets.accounts)
      )
    ),
  });

  for (const budget of userBudgets) {
    await updateBudgetRecord(budget, transaction.transactionDate);
  }
};
