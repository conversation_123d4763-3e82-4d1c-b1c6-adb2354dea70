import { createRoute, z } from "@hono/zod-openapi";

import { errorSchema, jsonContent, requestBody, validationErrorSchema } from "~/helpers/openapi";
import { StatusCodes } from "~/lib/status-codes";

import {
  budgetCreateSchema,
  budgetHistoryQuerySchema,
  budgetHistoryResponseSchema,
  budgetListQuerySchema,
  budgetListResponseSchema,
  budgetResponseSchema,
  budgetUpdateSchema,
} from "./schemas";

const tags = ["Budgets"];

export const listBudgets = createRoute({
  tags,
  path: "/budgets",
  method: "get",
  operationId: "listBudgets",
  summary: "List budgets",
  description: "Get all budgets for the current user",
  security: [{ JWT: [] }],
  request: {
    query: budgetListQuerySchema,
  },
  responses: {
    [StatusCodes.OK]: jsonContent(budgetListResponseSchema, "Budgets retrieved"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_ENTITY]: validationErrorSchema("Validation error"),
  },
});

export const createBudget = createRoute({
  tags,
  path: "/budgets",
  method: "post",
  operationId: "createBudget",
  summary: "Create budget",
  description: "Create a new budget",
  security: [{ JWT: [] }],
  request: { body: requestBody(budgetCreateSchema, "Budget data") },
  responses: {
    [StatusCodes.CREATED]: jsonContent(budgetResponseSchema, "Budget created"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_ENTITY]: validationErrorSchema("Validation error"),
  },
});

export const getBudget = createRoute({
  tags,
  path: "/budgets/{id}",
  method: "get",
  operationId: "getBudget",
  summary: "Get budget",
  description: "Get budget by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(budgetResponseSchema, "Budget retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Budget not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_ENTITY]: validationErrorSchema("Validation error"),
  },
});

export const updateBudget = createRoute({
  tags,
  path: "/budgets/{id}",
  method: "put",
  operationId: "updateBudget",
  summary: "Update budget",
  description: "Update budget by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    body: requestBody(budgetUpdateSchema, "Budget update data"),
  },
  responses: {
    [StatusCodes.OK]: jsonContent(budgetResponseSchema, "Budget updated"),
    [StatusCodes.BAD_REQUEST]: errorSchema("Bad request"),
    [StatusCodes.NOT_FOUND]: errorSchema("Budget not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_ENTITY]: validationErrorSchema("Validation error"),
  },
});

export const deleteBudget = createRoute({
  tags,
  path: "/budgets/{id}",
  method: "delete",
  operationId: "deleteBudget",
  summary: "Delete budget",
  description: "Delete budget by ID",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
  },
  responses: {
    [StatusCodes.NO_CONTENT]: { description: "Budget deleted" },
    [StatusCodes.NOT_FOUND]: errorSchema("Budget not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_ENTITY]: validationErrorSchema("Validation error"),
  },
});

// Route Types
export type ListBudgetsRoute = typeof listBudgets;
export type CreateBudgetRoute = typeof createBudget;
export type GetBudgetRoute = typeof getBudget;
export type UpdateBudgetRoute = typeof updateBudget;
export type DeleteBudgetRoute = typeof deleteBudget;
export type GetBudgetHistoryRoute = typeof getBudgetHistory;

export const getBudgetHistory = createRoute({
  tags,
  path: "/budgets/{id}/history",
  method: "get",
  operationId: "getBudgetHistory",
  summary: "Get budget history",
  description: "Get paginated budget records for a specific budget",
  security: [{ JWT: [] }],
  request: {
    params: z.object({
      id: z.string().uuid().openapi({ example: "123e4567-e89b-12d3-a456-************" }),
    }),
    query: budgetHistoryQuerySchema,
  },
  responses: {
    [StatusCodes.OK]: jsonContent(budgetHistoryResponseSchema, "Budget history retrieved"),
    [StatusCodes.NOT_FOUND]: errorSchema("Budget not found"),
    [StatusCodes.UNAUTHORIZED]: errorSchema("Unauthorized"),
    [StatusCodes.UNPROCESSABLE_ENTITY]: validationErrorSchema("Validation error"),
  },
});
