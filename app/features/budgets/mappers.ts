import type { BudgetRecord, BudgetRecordResponse, BudgetResponse, BudgetWithCurrentRecord } from "./types";

import { formatISO } from "date-fns";

export const mapBudgetRecordResponse = (record: BudgetRecord): BudgetRecordResponse => ({
  id: record.id,
  budgetId: record.budgetId,
  startDate: formatISO(record.startDate, { representation: "date" }),
  endDate: formatISO(record.endDate, { representation: "date" }),
  plannedAmount: record.plannedAmount,
  usedAmount: record.usedAmount,
  createdAt: record.createdAt.toISOString(),
  updatedAt: record.updatedAt.toISOString(),
});

export const mapBudgetResponse = (budget: BudgetWithCurrentRecord): BudgetResponse => ({
  id: budget.id,
  name: budget.name,
  description: budget.description,
  period: budget.period,
  type: budget.type,
  value: budget.value,
  accounts: budget.accounts,
  isArchived: budget.isArchived,
  currentRecord: budget.currentRecord ? mapBudgetRecordResponse(budget.currentRecord) : undefined,
  createdAt: budget.createdAt.toISOString(),
  updatedAt: budget.updatedAt.toISOString(),
});
