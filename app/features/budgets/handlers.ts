import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "~/types";
import type {
  CreateBudgetRoute,
  DeleteBudgetRoute,
  GetBudgetHistoryRoute,
  GetBudgetRoute,
  ListBudgetsRoute,
  UpdateBudgetRoute,
} from "./routes";

import { StatusCodes } from "~/lib/status-codes";

import {
  createBudget as createBudgetAction,
  deleteBudget as deleteBudgetAction,
  getBudgetById,
  getBudgetHistory as getBudgetHistoryAction,
  getBudgetsByUserId,
  updateBudget as updateBudgetAction,
} from "./actions";
import { mapBudgetRecordResponse, mapBudgetResponse } from "./mappers";

export const listBudgets: AppRouteHandler<ListBudgetsRoute> = async (c) => {
  const user = c.get("user");
  const query = c.req.valid("query");

  const budgets = await getBudgetsByUserId(user.id, query);

  return c.json(budgets.map(mapBudgetResponse), StatusCodes.OK);
};

export const createBudget: AppRouteHandler<CreateBudgetRoute> = async (c) => {
  const user = c.get("user");
  const data = c.req.valid("json");

  const budget = await createBudgetAction(user, data);

  return c.json(mapBudgetResponse(budget), StatusCodes.CREATED);
};

export const getBudget: AppRouteHandler<GetBudgetRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  const budget = await getBudgetById(id, user.id);

  if (!budget) {
    return c.json({ success: false, message: "Budget not found" }, StatusCodes.NOT_FOUND);
  }

  return c.json(mapBudgetResponse(budget), StatusCodes.OK);
};

export const updateBudget: AppRouteHandler<UpdateBudgetRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const data = c.req.valid("json");

  const updatedBudget = await updateBudgetAction(id, user, data);

  return c.json(mapBudgetResponse(updatedBudget), StatusCodes.OK);
};

export const deleteBudget: AppRouteHandler<DeleteBudgetRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");

  await deleteBudgetAction(id, user.id);

  return c.body(null, StatusCodes.NO_CONTENT);
};

export const getBudgetHistory: AppRouteHandler<GetBudgetHistoryRoute> = async (c) => {
  const user = c.get("user");
  const { id } = c.req.valid("param");
  const query = c.req.valid("query");

  const { items, total } = await getBudgetHistoryAction(id, user.id, query);

  const response = {
    items: items.map(mapBudgetRecordResponse),
    meta: { total, page: query.page, limit: query.limit, count: items.length },
  };

  return c.json(response, StatusCodes.OK);
};
