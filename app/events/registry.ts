import type { AppBindings } from "~/types";
import type { Events } from "./types";

import { defineHandlers } from "@hono/event-emitter";

import * as accounts from "~/features/accounts/event-handlers";
import * as budgets from "~/features/budgets/event-handlers";
import * as goals from "~/features/goals/event-handlers";

import {
  AccountDeletedEvent,
  AccountUpdatedEvent,
  TransactionCreatedEvent,
  TransactionDeletedEvent,
  TransactionUpdatedEvent,
} from "./types";

const handlers = defineHandlers<Events, AppBindings>({
  [TransactionCreatedEvent]: [accounts.transactionCreated, budgets.transactionCreated],
  [TransactionUpdatedEvent]: [accounts.transactionUpdated, budgets.transactionUpdated],
  [TransactionDeletedEvent]: [accounts.transactionDeleted, budgets.transactionDeleted],
  [AccountUpdatedEvent]: [goals.accountUpdated],
  [AccountDeletedEvent]: [goals.accountDeleted],
});

export default handlers;
