import type { Account } from "~/features/accounts/types";
import type { Transaction } from "~/features/transactions/types";

export const TransactionCreatedEvent = "transaction:created";

export const TransactionUpdatedEvent = "transaction:updated";

export const TransactionDeletedEvent = "transaction:deleted";

export const AccountUpdatedEvent = "account:updated";

export const AccountDeletedEvent = "account:deleted";

export type Events = {
  [TransactionCreatedEvent]: { transaction: Transaction };
  [TransactionUpdatedEvent]: { previousTransaction: Transaction; transaction: Transaction };
  [TransactionDeletedEvent]: { transaction: Transaction };
  [AccountUpdatedEvent]: { account: Account };
  [AccountDeletedEvent]: { account: Account };
};
