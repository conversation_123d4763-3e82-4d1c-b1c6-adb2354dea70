export const StatusCodes = {
  // 2xx Success
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,

  // 3xx Redirection
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  NOT_MODIFIED: 304,
  TEMPORARY_REDIRECT: 307,
  PERMANENT_REDIRECT: 308,

  // 4xx Client Errors
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_CONTENT: 422,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,

  // 5xx Server Errors
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

export const StatusMessages = {
  // 2xx Success
  [StatusCodes.OK]: "OK",
  [StatusCodes.CREATED]: "Created",
  [StatusCodes.ACCEPTED]: "Accepted",
  [StatusCodes.NO_CONTENT]: "No Content",

  // 3xx Redirection
  [StatusCodes.MOVED_PERMANENTLY]: "Moved Permanently",
  [StatusCodes.FOUND]: "Found",
  [StatusCodes.NOT_MODIFIED]: "Not Modified",
  [StatusCodes.TEMPORARY_REDIRECT]: "Temporary Redirect",
  [StatusCodes.PERMANENT_REDIRECT]: "Permanent Redirect",

  // 4xx Client Errors
  [StatusCodes.BAD_REQUEST]: "Bad Request",
  [StatusCodes.UNAUTHORIZED]: "Unauthorized",
  [StatusCodes.FORBIDDEN]: "Forbidden",
  [StatusCodes.NOT_FOUND]: "Not Found",
  [StatusCodes.METHOD_NOT_ALLOWED]: "Method Not Allowed",
  [StatusCodes.CONFLICT]: "Conflict",
  [StatusCodes.UNPROCESSABLE_CONTENT]: "Unprocessable Content",
  [StatusCodes.TOO_MANY_REQUESTS]: "Too Many Requests",

  // 5xx Server Errors
  [StatusCodes.INTERNAL_SERVER_ERROR]: "Internal Server Error",
  [StatusCodes.NOT_IMPLEMENTED]: "Not Implemented",
  [StatusCodes.BAD_GATEWAY]: "Bad Gateway",
  [StatusCodes.SERVICE_UNAVAILABLE]: "Service Unavailable",
  [StatusCodes.GATEWAY_TIMEOUT]: "Gateway Timeout",
} as const;
