import { describe, expect, it } from "vitest";

import { StatusCodes, StatusMessages } from "./status-codes";

describe("StatusCodes", () => {
  it("should have correct status code values", () => {
    expect(StatusCodes.OK).toBe(200);
    expect(StatusCodes.CREATED).toBe(201);
    expect(StatusCodes.BAD_REQUEST).toBe(400);
    expect(StatusCodes.NOT_FOUND).toBe(404);
    expect(StatusCodes.INTERNAL_SERVER_ERROR).toBe(500);
  });

  it("should have matching status messages for all status codes", () => {
    // Check that every status code has a corresponding message
    Object.entries(StatusCodes).forEach(([key, code]) => {
      expect(StatusMessages[code]).toBeDefined();
      expect(typeof StatusMessages[code]).toBe("string");
    });
  });

  it("should have correct status messages", () => {
    expect(StatusMessages[StatusCodes.OK]).toBe("OK");
    expect(StatusMessages[StatusCodes.CREATED]).toBe("Created");
    expect(StatusMessages[StatusCodes.BAD_REQUEST]).toBe("Bad Request");
    expect(StatusMessages[StatusCodes.NOT_FOUND]).toBe("Not Found");
    expect(StatusMessages[StatusCodes.INTERNAL_SERVER_ERROR]).toBe("Internal Server Error");
  });
});
