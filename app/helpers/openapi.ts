import type { ZodMediaTypeObject } from "@asteasolutions/zod-to-openapi";

import { z } from "@hono/zod-openapi";

export const requestBody = <T extends ZodMediaTypeObject["schema"]>(schema: T, description: string) => ({
  content: {
    "application/json": { schema },
  },
  description,
  required: true,
});

export const jsonContent = <T extends ZodMediaTypeObject["schema"]>(schema: T, description: string) => ({
  content: {
    "application/json": { schema },
  },
  description,
});

export const validationErrorSchema = (description: string) => ({
  content: {
    "application/json": {
      schema: z
        .object({
          success: z.boolean().default(false),
          errors: z.array(z.string()),
          message: z.string(),
        })
        .openapi("ValidationError"),
    },
  },
  description,
});

export const errorSchema = (description: string) => ({
  content: {
    "application/json": {
      schema: z
        .object({
          success: z.boolean().default(false),
          message: z.string(),
          stack: z.string().optional(),
        })
        .openapi("CommonError"),
    },
  },
  description,
});
