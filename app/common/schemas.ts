import { z } from "@hono/zod-openapi";

import { currencies, defaultCurrency } from "~/lib/currencies";

export const paginationQuery = () =>
  z
    .object({
      page: z.coerce.number().int().min(1).default(1).openapi({ example: 1, description: "Page number" }),
      limit: z.coerce
        .number()
        .int()
        .min(1)
        .max(100)
        .default(20)
        .openapi({ example: 20, description: "Items per page" }),
    })
    .openapi("PaginationQuery");

export const paginationMeta = () =>
  z
    .object({
      total: z.number().int().openapi({ example: 12, description: "Total number of items" }),
      page: z.number().int().openapi({ example: 1, description: "Current page" }),
      limit: z.number().int().openapi({ example: 20, description: "Items per page" }),
      count: z.number().int().openapi({ example: 12, description: "Number of items on current page" }),
    })
    .openapi("PaginationMeta");

export const currency = (_?: string) =>
  z.enum(currencies).openapi("Currency", { description: "ISO 4217 currency code", example: defaultCurrency });

export const decimal = () =>
  z
    .string()
    .regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/)
    .openapi("Decimal", {
      description: "Decimal number with up to 4 decimal places",
      example: "100.2500",
    });

export const accountTypes = ["cash", "card", "bank_account", "savings", "loan", "other"] as const;

export const accountType = (description?: string) =>
  z.enum(accountTypes).openapi("AccountType", { description, example: "cash" });

export const transactionTypes = ["income", "expense", "transfer"] as const;

export const transactionType = (description?: string) =>
  z.enum(transactionTypes).openapi("TransactionType", { description, example: "expense" });

export const budgetPeriods = ["week", "month", "quarter", "year"] as const;

export const budgetPeriod = (description?: string) =>
  z.enum(budgetPeriods).openapi("BudgetPeriod", { description, example: "month" });

export const budgetTypes = ["fixed", "percentage"] as const;

export const budgetType = (description?: string) =>
  z.enum(budgetTypes).openapi("BudgetType", { description, example: "fixed" });

export const taskPeriods = ["weekly", "monthly", "quarterly", "yearly"] as const;

export const taskPeriod = (description?: string) =>
  z.enum(taskPeriods).openapi("TaskPeriod", { description, example: "monthly" });

export const taskStatuses = ["active", "completed", "skipped"] as const;

export const taskStatus = (description?: string) =>
  z.enum(taskStatuses).openapi("TaskStatus", { description, example: "active" });
