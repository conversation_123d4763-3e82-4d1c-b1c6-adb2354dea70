# Development Guidelines for Finanze.Pro Backend

This document provides guidelines and instructions for developing and maintaining the Finanze.Pro Backend API.

## Build and Configuration

### Prerequisites
- [Bun](https://bun.sh/) runtime (latest version recommended)
- PostgreSQL database
- Redis server

### Environment Setup
1. Copy the `.env` file and configure the environment variables:
   ```bash
   cp .env.example .env
   ```

   Key environment variables:
   - `PORT`: The port on which the server runs (default: 5000)
   - `NODE_ENV`: The environment (development, production, etc.)
   - `APP_ENV`: The application environment (local, staging, production, etc.)
   - `DATABASE_URL`: The PostgreSQL database connection string
   - `JWT_SECRET`: The secret key for JWT authentication
   - `CURRENCYAPICOM_KEY`: API key for currency rate updates
   - `LOG_LEVEL`: The logging level (debug, info, warn, error)

2. Install dependencies:
   ```bash
   bun install
   ```

### Running the Application
- Development mode with hot reload:
  ```bash
  bun run dev
  ```

- Start the worker (in a separate terminal):
  ```bash
  bun run dev:worker
  ```

- Production mode:
  ```bash
  bun run start
  bun run start:worker
  ```

### Database Management
The project uses Drizzle ORM for database operations. The configuration is in `drizzle.config.ts`.

- Run database migrations:
  ```bash
  bun run drizzle-kit migrate
  ```

## Testing

### Running Tests
The project uses Vitest for testing. Tests can be run with:

```bash
bun run test
```

For an interactive UI to view and debug tests:

```bash
bun run test:ui
```

### Writing Tests
- Test files should be placed next to the files they test with the `.test.ts` or `.spec.ts` extension, or in `__tests__` directories.
- Use the Vitest API (`describe`, `it`, `expect`) for writing tests.

Example test structure:

```typescript
import { describe, it, expect } from "vitest";
import { functionToTest } from "./file-to-test";

describe("FunctionName", () => {
  it("should do something specific", () => {
    const result = functionToTest(input);
    expect(result).toBe(expectedOutput);
  });
});
```

### Test Relaxations
The ESLint configuration relaxes certain rules for test files:
- `@typescript-eslint/no-explicit-any` is turned off
- `@typescript-eslint/no-non-null-assertion` is turned off

## Code Style and Development Practices

### Code Formatting
The project uses Prettier for code formatting with the following configuration:
- Double quotes for strings
- Semicolons required
- 2-space indentation
- 120-character line width
- ES5 trailing commas
- Sorted imports using `@ianvs/prettier-plugin-sort-imports`

Format code with:
```bash
bun run format
```

### Linting
The project uses ESLint with TypeScript support. The configuration enforces:
- Strict TypeScript rules (no any, no unsafe operations, etc.)
- Code quality rules (prefer const, no var, etc.)
- Integration with Prettier for consistent formatting

Lint code with:
```bash
bun run lint
```

Fix linting issues automatically with:
```bash
bun run lint:fix
```

### Database changes
- Never try to create a migration file manually, just provide the necessary steps in the summary

### API Development
- Use the router factory pattern from `app/lib/router.ts`
- Use Zod for request validation and OpenAPI schema generation
- Use typed environment configuration in `app/lib/env.ts`
- Use status codes from `app/lib/status-codes.ts` for consistent HTTP responses

### Worker Tasks
The project uses BullMQ for background processing. Worker tasks are defined in `worker.ts`.

## Utilities

### Currency Rates
Update currency rates with:
```bash
bun run update-rates
```
