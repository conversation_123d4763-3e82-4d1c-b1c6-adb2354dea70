### **Feature Request: Recurring Financial Tasks**

---

### 📝 **Introduction**

This document outlines the requirements for a new **Tasks** feature. This functionality will allow users to define,
manage, and track recurring financial activities (like paying bills or making regular savings contributions) that
repeat over a set period. It automates the creation of periodic task instances and helps users stay on top of their
financial responsibilities.

---

### 🧑‍💻 **User Story**

As a user, I want to create recurring tasks for my regular financial duties, such as "Pay Rent" or "Monthly Savings,"
so I don't forget them. I want the application to automatically create a new task for me each period (e.g., every month).
I need to be able to manually mark each period's task as 'completed' or 'skipped'. I also want the option to link
financial transactions to a task to track my payments and have the task automatically marked as complete when
the payments match the target amount. This will help me manage my recurring financial obligations efficiently.

---

### ✅ **Acceptance Criteria**

#### **Task (`Task`) Management**

- A user **must** be able to create a new `Task` with a `title` and a `period` (`weekly`, `monthly`, `quarterly`, `yearly`).
- A `Task` **can** have an optional `description`, `amount`, and `due_day` (e.g., day of the week or month).
- Users **must** be able to view a paginated list of their active tasks.
- Users **must** be able to view and update the details of a single task.
- Users **must** be able to `archive` a task. Archived tasks **must** be excluded from all automatic actions (e.g., new record creation) and filtered from the default task list view.
- Users **must** be able to delete a task, which will also delete all its associated task records.

#### **Task Instance (`TaskRecord`) Logic**

- For each active (non-archived) `Task`, a new `TaskRecord` **must** be automatically created at the beginning of each new period.
- Each `TaskRecord` **must** have a `start_date`, `end_date`, a `status` (defaulting to 'active'), and an `amount` (defaulting to the parent `Task`'s amount).
- Users **must** be able to manually update the status of a `TaskRecord` to either `completed` or `skipped`.
- Users **must** be able to view a paginated history of `TaskRecord`s for a specific task, sorted from newest to oldest.

#### **Notifications (Placeholder)**

- A `Task` **can** have notification settings, such as "notify at period start" and "notify X days before due date/period end."
- These settings should be stored, but no actual notifications need to be sent at this stage.

#### **Transaction Integration**

- The `Transaction` model **must** be updated to include an optional `task_record_id` reference.
- When linking a transaction, the system **must** validate that the transaction's date falls within the `start_date` and `end_date` of the referenced `TaskRecord`.
- A new endpoint **must** allow users to assign, replace, and remove a list of transactions from the relevant `TaskRecord` based on the transaction dates.

#### **Automatic Completion**

- If a `Task` has a non-zero, non-null `amount`, its `TaskRecord` **must** be automatically marked as `completed` when
  the sum of its assigned transactions is equal to or greater than the `TaskRecord`'s `amount`.
- When this happens, the `TaskRecord`'s `amount` field **must** be updated to reflect the actual total amount of the assigned transactions.

---

### 🛠️ **Technical Details**

#### **Data Model: `Task`**

| Field                | Type    | Required | Description                                                    |
| :------------------- | :------ | :------- | :------------------------------------------------------------- |
| `id`                 | UUID    | Yes      | Unique identifier for the task.                                |
| `user_id`            | UUID    | Yes      | The user who owns the task.                                    |
| `title`              | String  | Yes      | Name of the task (e.g., "Pay Rent").                           |
| `period`             | Enum    | Yes      | Recurrence period: `weekly`, `monthly`, `quarterly`, `yearly`. |
| `description`        | String  | No       | Optional details about the task.                               |
| `amount`             | Number  | No       | Optional target amount for the task.                           |
| `due_day`            | Integer | No       | Optional day for completion target (e.g., day of week/month).  |
| `is_archived`        | Boolean | Yes      | Defaults to `false`. If `true`, task is inactive.              |
| `send_notifications` | Boolean | No       | Placeholder for notification settings. Defaults to `false`.    |

#### **Data Model: `TaskRecord`**

| Field        | Type   | Required | Description                                                           |
| :----------- | :----- | :------- | :-------------------------------------------------------------------- |
| `id`         | UUID   | Yes      | Unique identifier for the task instance.                              |
| `task_id`    | UUID   | Yes      | Foreign key referencing the parent `Task`.                            |
| `start_date` | Date   | Yes      | The start date of the period for this record.                         |
| `end_date`   | Date   | Yes      | The end date of the period for this record.                           |
| `status`     | Enum   | Yes      | `active`, `completed`, `skipped`. Defaults to `active`.               |
| `amount`     | Number | No       | Target amount, defaults to `Task.amount`. Updated on auto-completion. |

#### **Data Model Update: `Transaction`**

- Add an optional field: `task_record_id` (Type: UUID, nullable, foreign key to `TaskRecord`).

#### **API Endpoints**

Standard CRUD endpoints for the `tasks` resource:

- **`POST /tasks`**: Create a new `Task`.
- **`GET /tasks`**: Retrieve a paginated list of tasks for the authenticated user.
- **`GET /tasks/{id}`**: Retrieve a single `Task`.
- **`PUT /tasks/{id}`**: Update an existing `Task`.
- **`DELETE /tasks/{id}`**: Delete a `Task` and its records.

Additional endpoints for task management:

- **`GET /tasks/{id}/history`**: Retrieve a paginated list of `TaskRecord`s for a given task, sorted descending by `start_date`.
- **`POST /tasks/{id}/transactions`**: Assign transactions to the relevant `TaskRecord`.
  - **Body**: `{ "transactions": ["uuid1", "uuid2"] }`
- **`PUT /tasks/{id}/transactions`**: Replace all assigned transactions for the relevant `TaskRecord`.
  - **Body**: `{ "transactions": ["uuid3"] }`
- **`DELETE /tasks/{id}/transactions`**: Remove specified transactions from the relevant `TaskRecord`.
  - **Body**: `{ "transactions": ["uuid3"] }`
