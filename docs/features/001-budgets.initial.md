# 🧾 Feature: Budgets

The goal is to implement a new feature called **Budgets** that enables users to control and monitor their spending.

---

## 📘 General Description

- A **budget** always belongs to a specific user.
- Budgets help limit spending based on a **fixed amount** or a **percentage of income**.
- Budgets include:
  - **Name** (required)
  - **Description** (optional)
  - **Period**: Defines the budget cycle — can be one of:
    - `"week"`
    - `"month"`
    - `"quarter"`
    - `"year"`
  - **Type**:
    - `"fixed"`: A specific monetary value per period
    - `"percentage"`: A portion of the user's income
  - **Value**:
    - For `"fixed"`: the monetary amount allowed
    - For `"percentage"`: percentage (e.g., 20%)
  - **Account Restrictions (optional)**: One or more specific accounts the budget applies to
  - **Archived Flag**:
    - Archived budgets are ignored in calculations and reporting

---

## 📊 Budget Records

- Each **budget** automatically generates **usage records** for each period.
- These records track how much of the budgeted amount has been spent.

### `BudgetRecord` Fields:
- `start_date`: Start of the budget period (e.g., beginning of the month)
- `end_date`: End of the budget period
- `planned_amount`: Budgeted value for the period (calculated based on the budget definition)
- `used_amount`: Actual spending for the period, derived from transactions

---

## 🔁 Dynamic Record Generation

- On budget access, the system should:
  1. Determine the **current period** based on the budget's `period` and the current date.
  2. Check if a `BudgetRecord` exists for that period.
  3. If not, **create it on the fly** using:
    - `planned_amount` based on the budget type and value
    - `used_amount` calculated from the relevant transactions
    - If account restrictions are set, only consider transactions from those accounts.

---

## ⚙️ Live Updates via Events

- Budget `used_amount` should stay up-to-date by reacting to transaction events:
  - On **create**, **update**, or **delete** of a transaction
- This should use the **event system**, consistent with the existing `accounts` feature.

---

## 🌐 API Requirements

### CRUD Endpoints for Budgets:
- `POST /budgets` – Create a new budget
- `GET /budgets` – List all budgets (optionally exclude archived)
- `GET /budgets/:id` – Get budget details
- `PUT /budgets/:id` – Update a budget
- `DELETE /budgets/:id` – Delete or archive a budget

### Nested History Endpoint:
- `GET /budgets/:id/history`
  - Returns a **paginated list** of `BudgetRecords`
  - Ordered from **most recent to oldest**

### Additional Notes:
- The **current budget record** (based on today’s date and the budget period) should always be **included in the API response** for all requests related to the budget.
