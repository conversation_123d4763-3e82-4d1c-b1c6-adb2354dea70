### **Feature Update: Tasks - Status Control and On-Access Record Creation**

---

### 🚀 **Introduction**

This document outlines enhancements to the existing **Tasks** feature. The changes introduce a dedicated endpoint for more granular control over a task's status and ensure that task records are always up-to-date when a task is accessed, improving system robustness.

---

### ✨ **New Functionality**

#### **Dedicated Endpoint for Status Updates**

To provide a clearer and more direct way for users to manage their current progress, a new endpoint will be introduced.

- **Endpoint**: `PUT /tasks/{id}/status`
- **Description**: Sets the status of the **current active `TaskRecord`** for the specified parent `Task`. This provides a streamlined way to mark a task as completed or skipped without needing the `TaskRecord`'s specific ID.
- **Request Body**:
  - `status` (Enum, Required): The new status for the task record. Must be one of `completed` or `skipped`.
  - `amount` (Number, Optional): If the `status` is `completed`, this value can be provided to override the `TaskRecord`'s default amount. If not provided, the existing amount on the record is kept.
- **Example Payload**:
  ```json
  {
    "status": "completed",
    "amount": 155.5
  }
  ```

---

### ⚙️ **Changes to Existing Logic**

#### **On-Access Task Record Creation**

To guarantee data integrity and ensure a seamless user experience, the system's logic for `TaskRecord` creation will be enhanced.

- **Requirement**: Before any action is performed on a specific task (`GET /tasks/{id}`, `PUT /tasks/{id}/status`, `POST /tasks/{id}/transactions`, etc.), the system **must** perform a check.
- **Logic**:
  1.  Verify if the requested `Task` is active (`is_archived` is `false`).
  2.  Check if a `TaskRecord` already exists for the **current period** (e.g., for the current month if the task's period is `monthly`).
  3.  If the task is active and no record exists for the current period, a new `TaskRecord` **must** be created automatically "just-in-time" before the requested action proceeds.
- **Impact**: This change decouples `TaskRecord` creation from a purely time-based background job. It ensures that a user interacting with a task will always have a valid record for the current period to act upon, preventing errors and simplifying client-side logic. The original background job for creating records at the start of a period should still be maintained as the primary mechanism.
