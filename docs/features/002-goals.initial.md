### **Feature Request: Financial Goals Tracking**

---

### 🎯 **Introduction**

This document outlines the requirements for a new **Goals** feature. This feature will empower users to create, track, and manage their financial savings goals directly within the application, providing a clear visualization of their progress.

---

### 🧑‍💻 **User Story**

As a user, I want to be able to create financial goals to track my savings for specific purposes (e.g., "New Car," "Vacation Fund"). I want to link one or more of my existing accounts to a goal so that the application can automatically track my progress towards the target amount based on the current balance of the linked accounts. This will help me stay motivated and informed about my financial objectives.

---

### ✅ **Acceptance Criteria**

#### **Goal Creation & Management**

- A user **must** be able to create a new goal.
- A goal **must** have a `name` (required, string) and a list of `accounts` (required, at least one).
- A goal **can** have an optional `description` (string), `target_amount` (number), `target_date` (date), `color` (string, e.g., hex code), and an `icon_url` (string).
- Users **must** be able to view a list of all their goals.
- Users **must** be able to view the details of a single goal.
- Users **must** be able to update any of the fields of an existing goal.
- Users **must** be able to delete a goal.

#### **Account & Currency Logic**

- When creating or updating a goal, a user **can only** link active accounts that share the **same currency**.
- The currency of the linked accounts **must** be stored in a `currency` field on the goal object for reference.

#### **Progress Tracking**

- Each goal **must** have a `current_amount` field.
- The `current_amount` **must** be calculated and saved whenever a goal is created or updated by summing the current balances of all linked accounts.
- The `current_amount` **must** be automatically recalculated and updated whenever any of the linked accounts are updated (e.g., balance change) via an event-driven mechanism.

#### **Event-Driven Updates**

- When a linked account is updated, the corresponding goal's `current_amount` **must** be updated.
- If a linked account is deleted, its UUID **must** be removed from the goal's list of `accounts`.
- If the last remaining linked account is removed from a goal, the goal itself **must** be automatically deleted.

---

### 🛠️ **Technical Details**

#### **Data Model: `Goal`**

| Field            | Type        | Required | Description                                                     |
| :--------------- | :---------- | :------- | :-------------------------------------------------------------- |
| `id`             | UUID        | Yes      | Unique identifier for the goal.                                 |
| `user_id`        | UUID        | Yes      | The user who owns the goal.                                     |
| `name`           | String      | Yes      | Name of the goal.                                               |
| `accounts`       | Array[UUID] | Yes      | List of connected account UUIDs.                                |
| `target_amount`  | Number      | No       | The target financial amount for the goal.                       |
| `current_amount` | Number      | Yes      | The current combined balance of linked accounts. Defaults to 0. |
| `description`    | String      | No       | A short description of the goal.                                |
| `color`          | String      | No       | A hex color code for UI display.                                |
| `icon_url`       | String      | No       | URL for a goal icon/image.                                      |
| `target_date`    | Date        | No       | The date by which the user wants to achieve the goal.           |
| `currency`       | String      | Yes      | The currency of the linked accounts (e.g., "USD").              |

#### **API Endpoints (CRUD)**

Standard CRUD endpoints should be implemented for the `goals` resource:

- **`POST /goals`**: Create a new goal.
- **`GET /goals`**: Retrieve a list of all goals for the authenticated user.
- **`GET /goals/{id}`**: Retrieve a single goal by its ID.
- **`PUT /goals/{id}`**: Update an existing goal.
- **`DELETE /goals/{id}`**: Delete a goal.

#### **Event Handling**

- Subscribe to `account:updated` and `account:deleted` events.
- On `account:updated`, filter for goals that contain the updated account's UUID in their `accounts` list and trigger a recalculation of their `current_amount`.
- On `account:deleted`, find any goals linked to that account, remove the account UUID from the `accounts` array, and then check if the array is empty. If it is, delete the goal.
