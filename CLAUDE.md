# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Running the Application
- `bun run dev` - Start API server with hot reload
- `bun run dev:worker` - Start background worker process with hot reload
- `bun run start` - Start API server in production mode
- `bun run start:worker` - Start worker process in production mode

### Code Quality
- `bun run lint` - Run ESLint to check code quality
- `bun run lint:fix` - Fix ESLint issues automatically
- `bun run format` - Format code with Prettier

### Testing
- `bun run test` - Run tests with Vitest
- `bun run test:ui` - Run tests with Vitest UI

### Database
- `bun run drizzle-kit migrate` - Run database migrations
- `bun run drizzle-kit generate` - Generate new migrations from schema changes (developer should run this after schema modifications)

### Utilities
- `bun run update-rates` - Update currency exchange rates

## Architecture Overview

### Tech Stack
- **Runtime**: Bun
- **Web Framework**: Hono with OpenAPI/Zod integration
- **Database**: PostgreSQL with Dr<PERSON>zle ORM
- **Background Jobs**: BullMQ with Redis
- **Validation**: Zod schemas with OpenAPI documentation
- **Testing**: Vitest

### Project Structure
- `app/` - Main application code
  - `features/` - Feature-based modules (auth, accounts, budgets, categories, transactions, users)
  - `db/` - Database schemas, migrations, and configuration
  - `lib/` - Shared utilities (env config, router factory, logger)
  - `tasks/` - Background job processing
  - `events/` - Event system for inter-feature communication
- `worker.ts` - Background worker entry point

### Key Patterns

**Router Factory**: Use `createRouter()` from `app/lib/router.ts` for consistent Hono setup with OpenAPI integration and validation error handling.

**Feature Structure**: Each feature follows the same pattern:
- `router.ts` - Route definitions with OpenAPI schemas
- `handlers.ts` - HTTP request handlers
- `actions.ts` - Business logic functions
- `schemas.ts` - Zod validation schemas
- `types.ts` - TypeScript type definitions
- `mappers.ts` - Data transformation functions

**Database Access**: 
- Use Drizzle ORM with schemas in `app/db/schemas/`
- All tables include common fields (id, createdAt, updatedAt) via shared schema helpers
- Migrations are in `app/db/migrations/` - when modifying schemas, developer must run `bun run drizzle-kit generate` to create migrations

**Authentication**: JWT-based with middleware that excludes public endpoints (auth/token, user registration, docs)

**Event System**: Features communicate via events registered in `app/events/registry.ts`

**Background Jobs**: BullMQ queues defined in `app/tasks/queues.ts` with processors in `app/tasks/processors/`

### Environment Configuration
Environment variables are typed and validated in `app/lib/env.ts`. Key variables include:
- `DATABASE_URL` - PostgreSQL connection
- `JWT_SECRET` - Authentication secret
- `CURRENCYAPICOM_KEY` - Currency API key
- `PORT` - Server port (default: 5000)

### API Documentation
- OpenAPI spec available at `/api/openapi.json`
- Interactive docs at `/api/docs` (Scalar UI)
- Bull dashboard at `/admin/bull` for monitoring background jobs